package com.paic.ncbs.claim.model.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@ApiModel("理赔批量结算主信息")
@Data
public class BatchPaymentMainInfo {
    @ApiModelProperty("打包批次号")
    private String batchNo;

    @ApiModelProperty("类型：3-理赔")
    private String batchType;

    @ApiModelProperty("合作方代码")
    private String parterCode;

    @ApiModelProperty("合作方名称")
    private String parterName;

    @ApiModelProperty("收款户名")
    private String payeeName;

    @ApiModelProperty("批次号下总结算金额")
    private BigDecimal sumAmount;

    @ApiModelProperty("批次号下数据条数")
    private Integer sumCount;

    @ApiModelProperty("是否有发票，1-有发票，0-无发票(固定值-0)")
    private String isExistInvoice;

    @ApiModelProperty("支付状态及数据校验状态回调接口")
    private String payStatusReturnURL;

    private String invoiceReturnURL;

}
