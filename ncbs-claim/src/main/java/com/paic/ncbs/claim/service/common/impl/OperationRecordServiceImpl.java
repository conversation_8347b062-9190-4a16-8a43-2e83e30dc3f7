package com.paic.ncbs.claim.service.common.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.enums.TaskNodeEnum;
import com.paic.ncbs.claim.common.response.GlobalResultStatus;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.dao.entity.common.OperationRecordEntity;
import com.paic.ncbs.claim.dao.mapper.common.OperationRecordMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.vo.record.OperationRecordVO;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 操作记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-23
 */
@Service
@Slf4j
public class OperationRecordServiceImpl extends ServiceImpl<OperationRecordMapper, OperationRecordEntity> implements IOperationRecordService {


    @Override
    public void insertOperationRecord(String reportNo, String operationNode, String description, String remark, String operationUser) {
        if (StringUtils.isEmptyStr(reportNo) || StringUtils.isEmptyStr(operationNode)) {
            log.error("保存操作记录案件号或节点为空，reportNo:{}, operationNode:{}", reportNo, operationNode);
            return;
        }
        try {
            OperationRecordEntity operationRecordEntity = new OperationRecordEntity();
            operationRecordEntity.setReportNo(reportNo);
            operationRecordEntity.setOperationTime(new Date());
            operationRecordEntity.setOperationUser(operationUser);
            operationRecordEntity.setOperationNode(operationNode);
            operationRecordEntity.setDescription(description);
            operationRecordEntity.setRemark(remark);
            operationRecordEntity.setCreatedBy(operationUser);
            operationRecordEntity.setUpdatedBy(operationUser);
            operationRecordEntity.setMainNode(TaskNodeEnum.getName(operationNode));
            this.save(operationRecordEntity);
        } catch (Exception e) {
            log.error("保存操作记录异常，reportNo:{}, operationNode:{}, description:{}，message：{}", reportNo, operationNode, description, e.getMessage(), e);
        }
    }

    @Override
    public void insertOperationRecordByLabour(String reportNo, String operationNode, String description, String remark) {
        String userId = WebServletContext.getUserId();
        if (StringUtils.isEmptyStr(userId)) {
            log.error("操作记录获取操作人为空，reportNo:{}, operationNode:{}, description:{}", reportNo, operationNode, description);
            return;
        }
        this.insertOperationRecord(reportNo, operationNode, description, remark, userId);
    }

    @Override
    public void insertDispatchRecord(String reportNo, String operationNode, Boolean isAutoDispatch, String targetUser, String operationUser) {
        if (StringUtils.isEmptyStr(reportNo) || StringUtils.isEmptyStr(operationNode)) {
            log.error("保存调度记录案件号或节点为空，reportNo:{}, operationNode:{}", reportNo, operationNode);
            return;
        }
        try {
            OperationRecordEntity operationRecordEntity = new OperationRecordEntity();
            operationRecordEntity.setOperationType("1");
            operationRecordEntity.setReportNo(reportNo);
            operationRecordEntity.setOperationTime(new Date());
            operationRecordEntity.setOperationUser(operationUser);
            operationRecordEntity.setOperationNode(operationNode);
            operationRecordEntity.setTargetUser(targetUser);
            operationRecordEntity.setCreatedBy(operationUser);
            operationRecordEntity.setUpdatedBy(operationUser);
            operationRecordEntity.setDescription(TaskNodeEnum.getName(operationNode) + "调度");
            operationRecordEntity.setMainNode("调度");
            if (isAutoDispatch) {
                operationRecordEntity.setRemark("自动调度至：" + targetUser);
            } else {
                operationRecordEntity.setRemark("手工调度至：" + targetUser);
            }
            this.save(operationRecordEntity);
        } catch (Exception e) {
            log.error("保存操作记录异常，reportNo:{}, operationNode:{}, isAutoDispatch:{}，message：{}", reportNo, operationNode, isAutoDispatch, e.getMessage(), e);
        }
    }

    @Override
    public void insertDispatchRecordByLabour(String reportNo, String operationNode, Boolean isAutoDispatch, String targetUser) {
        String userId = WebServletContext.getUserId();
        if (StringUtils.isEmptyStr(userId)) {
            log.error("调度记录获取操作人为空，reportNo:{}, operationNode:{}, targetUser:{}", reportNo, operationNode, targetUser);
            return;
        }
        this.insertDispatchRecord(reportNo, operationNode, isAutoDispatch, targetUser, userId);
    }

    @Override
    public List<OperationRecordVO> queryRecord(String reportNo) {
        if(StringUtils.isEmptyStr(reportNo)){
            throw new GlobalBusinessException(GlobalResultStatus.ERROR.getCode(),GlobalResultStatus.ERROR.format("参数不能为空"));
        }
        List<OperationRecordVO> resp = new ArrayList<>();
        List<OperationRecordEntity> operationRecordEntityList = this.lambdaQuery()
                .eq(OperationRecordEntity::getReportNo, reportNo)
                .eq(OperationRecordEntity::getDelFlag, "N")
                .orderByDesc(OperationRecordEntity::getOperationTime).list();
        if(!CollectionUtils.isEmpty(operationRecordEntityList)){
            for (OperationRecordEntity operationRecordEntity : operationRecordEntityList) {
                OperationRecordVO operationRecordVO = new OperationRecordVO();
                BeanUtils.copyProperties(operationRecordEntity, operationRecordVO);
                operationRecordVO.setOperationTimeMillis(operationRecordEntity.getOperationTime()); // 赋值精度到毫秒
                resp.add(operationRecordVO);
            }
        }
        return resp;
    }
}
