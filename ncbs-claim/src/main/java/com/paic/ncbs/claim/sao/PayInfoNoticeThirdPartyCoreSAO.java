package com.paic.ncbs.claim.sao;


import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.model.vo.pay.PaymentItemVO;
import com.paic.ncbs.claim.replevy.vo.PayThawBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 第三方系统-支付对接
 * <AUTHOR>
 */
public interface PayInfoNoticeThirdPartyCoreSAO {

    /**
     * 保单批单送收付
     *
     * 新健康险理赔同步核心理赔后，调用该接口。
     */
    void noticePayment(String reportNo, Integer caseTimes, String paySerialNo, boolean isVerifyFirstPay, boolean isPrePay);

    /**
     * 理赔费用发票 送收付Q13
     * @param paymentItemDTO
     */
    void sendClaimVatInfo(PaymentItemDTO paymentItemDTO);

    /**
     * 费用发票修改发送收付费
     * @param feeInvoiceSendPaymentDTO
     */
    void feeInvoiceModifySendPayment(String reportNo, FeeInvoiceSendPaymentDTO feeInvoiceSendPaymentDTO);

    /**
     * 获取支付凭证
     * @param paymentItemDTO
     * @return 支付凭证COS地址
     */
    String queryPaymenVoucherUrl(PaymentItemDTO paymentItemDTO);

    /**
     * 合并支付
     */
    void sendMergePayment(String batchNo, BatchPaymentInfo batchPaymentInfo,
                          List<BatchPaymentDetailInfo> batchPaymentDetailInfo);

    /**
     * 追偿送收付
     * @param reportNo
     * @param paySerialNo
     */
    void noticeReplevyPayment(List<PaymentItemDTO> allItems,String reportNo, String paySerialNo);

    /**
     * 收付确认上报接口
     *
     * @param payThawBody
     * @param reportNo
     * @return
     */
    String sendPaymentConfirm(PayThawBody payThawBody,String reportNo,Integer caseTimes);
    /**
     * 关联实收冻结解冻接口
     *
     * @param  freezeFlag F-冻结，R-解冻
     * @return
     */
    PayResult sendPayThaw(String reportNo,Integer caseTimes,String bankFlowNo, BigDecimal sumAmount, String orgBusinessNo, String freezeFlag );

    Map<String, List<PaymentItemDTO>> getPaymentItemAndFeeItem(String reportNo, Integer caseTimes, String paymentStatus);

    void sendMergePaymentReturn(PaymentItemDTO itemDTOm, PaymentInfoVO paymentInfoVO);

    void sendPaymentOtherReturn(PayAccountInfo payAccountInfo,PaymentItemDTO itemDto);

    List<PaymentItemDTO> sendClaimVatCoinsInfo(List<PaymentItemDTO> fullPayItems, String reportNo,Integer caseTimes);
}
