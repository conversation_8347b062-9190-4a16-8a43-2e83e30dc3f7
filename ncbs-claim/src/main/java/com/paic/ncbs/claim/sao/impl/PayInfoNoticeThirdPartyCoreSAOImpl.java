package com.paic.ncbs.claim.sao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.constant.*;
import com.paic.ncbs.claim.common.enums.*;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.pay.ClmsPaymentPlan;
import com.paic.ncbs.claim.dao.entity.report.ReportCustomerInfoEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.coinsurance.PaymentItemFeeMapper;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyPlanMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.EndCaseMapper;
import com.paic.ncbs.claim.dao.mapper.fee.FeePayMapper;
import com.paic.ncbs.claim.dao.mapper.ocas.OcasMapper;
import com.paic.ncbs.claim.dao.mapper.other.CommonParameterMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentInfoMapper;
import com.paic.ncbs.claim.dao.mapper.pay.PaymentItemMapper;
import com.paic.ncbs.claim.dao.mapper.pay.SendPaymentRecordMapper;
import com.paic.ncbs.claim.dao.mapper.report.InsuredPersonMapper;
import com.paic.ncbs.claim.dao.mapper.settle.CoinsureRecordMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import com.paic.ncbs.claim.model.dto.mq.syncstatus.SyncCaseStatusDto;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPayDTO;
import com.paic.ncbs.claim.model.dto.other.AsynchronousCompensationJobExtDTO;
import com.paic.ncbs.claim.model.dto.pay.*;
import com.paic.ncbs.claim.model.dto.pay.BatchPaymentDetailInfo;
import com.paic.ncbs.claim.model.dto.report.InsuredPersonDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureDTO;
import com.paic.ncbs.claim.model.dto.settle.CoinsureRecordDTO;
import com.paic.ncbs.claim.model.dto.settle.PolicyPlanDTO;
import com.paic.ncbs.claim.model.vo.pay.PaymentInfoVO;
import com.paic.ncbs.claim.mq.producer.MqProducerSyncCaseStatusService;
import com.paic.ncbs.claim.replevy.dao.ClmsRelatedActualReceiptMapper;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyChargeMapper;
import com.paic.ncbs.claim.replevy.dao.ClmsReplevyMainMapper;
import com.paic.ncbs.claim.replevy.dto.PlanAndDutyQueryDTO;
import com.paic.ncbs.claim.replevy.entity.ClmsRelatedActualReceipt;
import com.paic.ncbs.claim.replevy.entity.ClmsReplevyCharge;
import com.paic.ncbs.claim.replevy.vo.*;
import com.paic.ncbs.claim.sao.PayInfoNoticeThirdPartyCoreSAO;
import com.paic.ncbs.claim.service.ahcs.AhcsPolicyPlanService;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.service.doc.PrintService;
import com.paic.ncbs.claim.service.pay.ClmsPaymentPlanService;
import com.paic.ncbs.claim.service.product.ProductService;
import com.paic.ncbs.claim.service.report.ReportCustomerInfoService;
import com.paic.ncbs.claim.service.report.ReportInfoService;
import com.paic.ncbs.claim.service.restartcase.RestartCaseService;
import com.paic.ncbs.claim.service.schedule.JobService;
import com.paic.ncbs.claim.service.schedule.impl.JobServiceImpl;
import com.paic.ncbs.claim.service.settle.CoinsureService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.policy.dto.PlanDTO;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.paic.ncbs.claim.common.constant.SettleConst.CLAIM_TYPE_PAY;
import static com.paic.ncbs.claim.common.constant.SettleConst.CLAIM_TYPE_PRE_PAY;

/**
 * 第三方系统-支付对接
 */
@Slf4j
@Service
@RefreshScope
public class PayInfoNoticeThirdPartyCoreSAOImpl implements PayInfoNoticeThirdPartyCoreSAO {

    @Value("${ncbs.pay.url:http://10.18.31.239:8868/fin/fin-web/web/api/transData/transToPayment}")
    private String payUrl;

    @Value("${ncbs.pay.passWord:testPassword}")
    private String passWord;

    @Value("${ncbs.pay.userCode:test}")
    private String userCode;

    @Value("${switch.mesh}")
    private Boolean switchMesh;

    @Value("${ncbs.pay.invoiceReturnURL:http://ncbs-claim.lb.ssdev.com:48915/claim/public/pay/feeInvoiceBackResult}")
    private String invoiceReturnURL;

    @Value("${ncbs.pay.skipcase}")
    private String skipCase;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    PaymentItemMapper paymentItemMapper;
    @Autowired
    OcasMapper ocasMapper;
    @Autowired
    CoinsureService coinsureService;
    @Autowired
    ReportCustomerInfoService reportCustomerInfoService;
    @Autowired
    ClmsPaymentPlanService clmsPaymentPlanService;
    @Autowired
    FeePayMapper feePayMapper;
    @Autowired
    PrintService printService;
    @Autowired
    CommonParameterMapper commonParameterMapper;
    @Autowired
    AhcsPolicyPlanService ahcsPolicyPlanService;
    @Autowired
    private CoinsureRecordMapper coinsureRecordMapper;
    @Autowired
    private SendPaymentRecordMapper sendPaymentRecordMapper;

    @Autowired
    private ProductService productService;
    @Autowired
    private ReportInfoService reportInfoService;
    @Autowired
    private EndCaseMapper endCaseMapper;
    @Autowired
    private PaymentInfoMapper paymentInfoMapper;
    @Autowired
    private InsuredPersonMapper insuredPersonMapper;
    @Autowired
    private PaymentItemFeeMapper paymentItemFeeMapper;
    @Autowired
    private JobServiceImpl jobService;
    @Autowired
    private MqProducerSyncCaseStatusService mqProducerSyncCaseStatusService;
    @Autowired
    private ClmsReplevyChargeMapper clmsReplevyChargeMapper;
    @Autowired
    private ClmsRelatedActualReceiptMapper clmsRelatedActualReceiptMapper;
    @Autowired
    private ClmsReplevyMainMapper clmsReplevyMainMapper;
    @Value("${ncbs.pay.reportSubModeConfig:9-04,9-05,9-06}")
    private String reportSubModeConfig;
    @Autowired
    private RestartCaseService restartCaseService;
    @Value("${ncbs.pay.paymentBackURL:http://ncbs-claim.lb.ssdev.com:48915/claim}")
    private String paymentBackURL;
    @Autowired
    private AhcsPolicyPlanMapper policyPlanMapper;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private GlobalPolicyService globalPolicyService;

    /**
     * 一期只考虑 13 赔款 1J直接理赔费用
     * claimType=1 赔款类型：(13-赔款 1J直接理赔费用 "C13", "共保代付赔款" "C1J", "共保代付费用"
     * claimType=2 11-预赔赔款 "11J", "预赔费用" ,"P13", "共保代付预赔赔款" "P1J", "共保代付预赔费用" )
     *
     * @param reportNo 报案号
     * @param caseTimes 重开次数  1 的时候为首次，后续重开依次加1
     * @param paySerialNo 二次支付的时候传过来的唯一标识
     * @param isVerifyPay 第一次核赔提交送收付的时候需要考虑案件重开的特殊场景 是否是核赔处提交
     * @param isPrePay 是否预赔
     */
    @Override
    public void noticePayment(String reportNo, Integer caseTimes, String paySerialNo, boolean isVerifyPay, boolean isPrePay) {
        try {
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(reportNo);
            paymentItemDTO.setCaseTimes(caseTimes);
            paymentItemDTO.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_10);
            List<PaymentItemDTO> allItems = paymentItemMapper.getPaymentItem(paymentItemDTO);
            List<PaymentItemDTO> feeItems =  paymentItemMapper.getPaymentItemFee(paymentItemDTO);
            //将费用支付项目替换为子表支付项
            Map<String,List<PaymentItemDTO>> feeItemMap = this.getPaymentItemAndFeeItem(reportNo,caseTimes,Constants.PAYMENT_ITEM_STATUS_10);
            allItems.stream()
                    .filter(item->item.getPaymentType().contains("J")).collect(Collectors.toList())
                    .forEach(fee ->{
                        List<PaymentItemDTO> feeItemList = feeItemMap.get(fee.getIdClmPaymentItem());
                        if(feeItemList!=null && feeItemList.size()>0){
                            allItems.remove(fee);
                            allItems.addAll(feeItemList);
                        }
                    });

            if (CollectionUtils.isEmpty(allItems)) {
                throw new GlobalBusinessException("未查到待支付的支付项信息！");
            }
            ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
            ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
            String reportSubMode = reportInfo.getReportSubMode();
            // 查理赔理算保单赔付金额
            Map<String, List<PaymentItemDTO>> itemGroupByPolicyNoMap = allItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getPolicyNo));
            log.info("核赔通过查理赔理算保单赔付金额={}", JsonUtils.toJsonString(itemGroupByPolicyNoMap));
            itemGroupByPolicyNoMap.forEach((k, v) -> {
                PaymentItemDTO itemDTO = v.get(0);
                ClaimSettlementPayment claimSettlementPayment = new ClaimSettlementPayment();
                PolicyMainInfo policyMainInfo = getPolicyMainInfo(customerInfo, k);
                claimSettlementPayment.setPolicyMainInfo(policyMainInfo);
                AdjustMaiInfo adjustMaiInfo = getAdjustMaiInfo(k, itemDTO, isPrePay);
                claimSettlementPayment.setAdjustMaiInfo(adjustMaiInfo);
                List<AdjustFee> adjustFees = getAdjustFees(paySerialNo, v, isVerifyPay,reportSubMode);
                claimSettlementPayment.setAdjustFee(adjustFees);
                // 接口调用需要的参数
                log.info("调用支付接口需要的参数={}",JsonUtils.toJsonString(adjustFees));
                if (!CollectionUtils.isEmpty(adjustFees)) {
                    sendPayment(paySerialNo, v, claimSettlementPayment, true);
                } else {
                    // 根据报案号 判断报案号下的赔款 是否都已经支付成功 如果是 通知 渠道
                    int noPay = paymentItemMapper.getNoPayItemByReportNo(reportNo, caseTimes);
                    if (noPay == 0) {
                        SyncCaseStatusDto syncCaseStatusDto = new SyncCaseStatusDto();
                        syncCaseStatusDto.setReportNo(reportNo);
                        syncCaseStatusDto.setCaseTimes(caseTimes);
                        syncCaseStatusDto.setCaseStatus(SyncCaseStatusEnum.PAY);
                        mqProducerSyncCaseStatusService.syncCaseStatus(syncCaseStatusDto);
                    }
                }
            });
            if (StringUtils.isEmptyStr(paySerialNo)) {
                //主共保
                Boolean isMainCoinsFlag = coinsureService.isMainCoinsureFlag(reportNo);
                log.info("批量送收付入参："+isMainCoinsFlag+"_"+JsonUtils.toJsonString(feeItems));
                if(isMainCoinsFlag){
                    if(ObjectUtil.isNotEmpty(feeItems)){
                        //按照支付项目送理赔批量接口
                        List<PaymentItemDTO> returnList = sendClaimVatCoinsInfo(feeItems,reportNo,caseTimes);
                        //allItem送价税分离的接口过滤掉已经送批量接口的数据
                        allItems.removeAll(returnList);
                    }
                }

                allItems.forEach(item -> {
                    if (PaymentTypeEnum.FEE.getType().equals(item.getPaymentType())
                            || PaymentTypeEnum.PRE_FEE.getType().equals(item.getPaymentType())) {
                        sendClaimVatInfo(item.getIdClmPaymentItem(), reportNo, item.getPolicyNo(),false);
                        item.setFinancePaymentAmount(item.getCoinsuranceActualAmount());
                        paymentItemMapper.updatePaymentItem(item);
                    }
                });
            }
            // 考虑特殊情况 案件重开 赔错保单的 前一次有赔付该保单 本次没有该保单赔付的场景
            /*if (caseTimes > 1 && isVerifyPay) {
                handleLastPayButThisNot(reportNo, caseTimes, allItems, customerInfo);
            }*/

            // 保存共保记录
            saveCoinsureRecord(reportNo, caseTimes, itemGroupByPolicyNoMap);
        } catch (GlobalBusinessException e) {
            log.error("收付费接口异常",e);
        }
    }

    public Map<String, List<PaymentItemDTO>> getPaymentItemAndFeeItem(String reportNo, Integer caseTimes, String paymentItemStatus) {
        List<PaymentItemDTO> paymentItemDTOS = paymentItemFeeMapper.getPaymentItemAndFeeItem(reportNo,caseTimes,paymentItemStatus);
        return paymentItemDTOS.stream().collect(Collectors.groupingBy(PaymentItemDTO::getIdClmPaymentItem));
    }

    @Override
    public void sendMergePaymentReturn(PaymentItemDTO itemDTO, PaymentInfoVO paymentInfoVO) {
        LogUtil.info("支付信息修改开始执行："+JsonUtils.toJsonString(itemDTO));
        try{
            //查询批次号
            String batchNo = sendPaymentRecordMapper.getBatchNo(itemDTO.getId());
            //获取接口入参
            PayAccountInfo payAccountInfo = new PayAccountInfo();
            payAccountInfo.setSettlementNo(batchNo);
            payAccountInfo.setAccountNo(paymentInfoVO.getClientBankAccount());
            payAccountInfo.setAccountName(paymentInfoVO.getClientName());
            payAccountInfo.setBankPayType(paymentInfoVO.getBankAccountAttribute());
            payAccountInfo.setBankCode(paymentInfoVO.getClientBankCode());
            payAccountInfo.setBankName(paymentInfoVO.getClientBankName());
            payAccountInfo.setBrachBankName(paymentInfoVO.getClientBankName());
            if(ObjectUtil.isNotEmpty(payAccountInfo)){
                this.sendPaymentOtherReturn(payAccountInfo,itemDTO);
            }else{
                throw new GlobalBusinessException("送收付数据为空，请确认");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void sendPaymentOtherReturn(PayAccountInfo payAccountInfo,PaymentItemDTO itemDTO) {
        log.info("sendPaymentOtherReturn准备调用收付接口:{}",JsonUtils.toJsonString(payAccountInfo));
        PayInfoNotice<PayAccountInfoVo> payInfoNotice = new PayInfoNotice<>();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        payInfoHead.setRequestType(PayInfoHead.TYPE_Q39);
        payInfoHead.setUserCode(userCode);
        payInfoNotice.setHead(payInfoHead);
        PayAccountInfoVo payAccountInfoVo = new PayAccountInfoVo();
        payAccountInfoVo.setPayAccountInfo(payAccountInfo);
        payInfoNotice.setBody(payAccountInfoVo);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        log.info("sendPaymentOtherReturn-payInfoNotice:" + JSON.toJSONString(payInfoNotice));
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        String status = "0";
        log.info("sendPaymentOtherReturn准备调用支付接口开始加签",JsonUtils.toJsonString(payAccountInfo));
        // 加签
        try {
            log.info("sendPaymentOtherReturn-payInfoNotice-payUrl:" + payUrl);
            String result;
            if (switchMesh) {
                log.info("sendPaymentOtherReturn-调用支付接口用mesh方式");
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json;charset:utf-8");
                result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice), headers);
            } else {
                result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
            }

            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("sendPaymentOtherReturn-success:" + jsonObject);
                payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                if (!payResult.isSuccess(payResult.getResponseCode())) {
                    log.info("sendPaymentOtherReturn-fail: 收付费返回失败");
                    log.error("sendPaymentOtherReturn-fail-errorCode: " + payResult.getErrorCode());
                    log.error("sendPaymentOtherReturn-fail-errorMessage: " + payResult.getErrorMessage());
                    status = "1";
                }
            } else {
                log.info("sendPaymentOtherReturn-fail: 调用接口异常");
                status = "1";
            }
        } catch (Exception e) {
            log.error("sendPaymentOtherReturn收付费接口调用异常", e);
            status= "1";
        }
        // 存送收付记录
        SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
        sendPaymentRecord.setCreatedBy("SYSTEM");
        sendPaymentRecord.setUpdatedBy("SYSTEM");
        sendPaymentRecord.setCreatedDate(new Date());
        sendPaymentRecord.setUpdatedDate(new Date());
        sendPaymentRecord.setReportNo(itemDTO.getReportNo());
        sendPaymentRecord.setRemark("");
        sendPaymentRecord.setCaseTimes(itemDTO.getCaseTimes());
        sendPaymentRecord.setPaySerialNo(itemDTO.getIdClmPaymentItem());
        sendPaymentRecord.setRequestType(payInfoHead.getRequestType());
        sendPaymentRecord.setRequestTypeDesc(payInfoHead.getRequestType());
        sendPaymentRecord.setRequestParam(JsonUtils.toJsonString(payInfoNotice));
        sendPaymentRecord.setResponseParam(JsonUtils.toJsonString(payResult));
        sendPaymentRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
        sendPaymentRecordMapper.insertSelective(sendPaymentRecord);

        //更新主信息batchPaymentMainInfo merge_payment_status 为送11-收付成功 一开始为10草稿
        if("0".equals(status)){
            String paymentStatus = "11";
            paymentItemMapper.updateOtherPaymentStatus(itemDTO.getIdClmPaymentItem(),paymentStatus);
            paymentItemFeeMapper.updateOtherPaymentStatus(itemDTO.getIdClmPaymentItem(),paymentStatus);
        }
        if("1".equals(status)){
            throw new GlobalBusinessException(payResult.getErrorMessage());
        }
    }

    public List<PaymentItemDTO> sendClaimVatCoinsInfo(List<PaymentItemDTO> fullPayItems, String reportNo,Integer caseTimes) {
        LogUtil.info("理赔批量开始执行："+reportNo);
        try {
            Map<String,List<PaymentItemDTO>> feeItemMap = this.getPaymentItemAndFeeItem(reportNo,caseTimes,null);
            List<PaymentItemDTO> returnList = new ArrayList<>();
            List<PlanDTO> planDTOS = policyPlanMapper.getPlanTaxRate(reportNo);
            Map<String,Double> planTaxMap = planDTOS.stream().collect(Collectors.toMap(plan->
                    plan.getPlanCode(),PlanDTO::getTaxRate
            ));
            String finalReportNo = reportNo;
            fullPayItems.forEach(v->{
                log.info("理赔Q18送全额给付item数据"+JsonUtils.toJsonString(v));
                //fee表数据
                List<PaymentItemDTO> paymentItemDTOList = feeItemMap.get(v.getIdClmPaymentItem());
                if(ObjectUtil.isNotEmpty(paymentItemDTOList)) {
                    //总额
                    BigDecimal sumAmount = paymentItemDTOList.stream()
                            .map(PaymentItemDTO::getCoinsuranceActualAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    List<String> idList = new ArrayList<>();
                    //明细
                    List<BatchPaymentDetailInfo> batchPaymentDetailInfoList = new ArrayList<>();
                    List<InvoiceTypeTaxInfoDTO> invoiceTypeList = feePayMapper.getInvoiceTypeList(v.getIdClmPaymentItem());
                    if (ObjectUtil.isNotEmpty(invoiceTypeList)) {
                        //查询是否普票
                        Boolean taxFlag = false;
                        String invoiceType = invoiceTypeList.get(0).getInvoiceType();
                        taxFlag = InvoiceTypeEnum.isExistSpecialInvoice(invoiceType);
//                        if (!"000".equals(invoiceType)) {
                            Boolean finalTaxFlag = taxFlag;
                            paymentItemDTOList.stream().forEach(item -> {
                                log.info("理赔Q18送全额给付itemFee数据"+JsonUtils.toJsonString(v));
                                //无发票不送明细数据
                                idList.add(item.getIdClmPaymentItem());
                                BatchPaymentDetailInfo batchPaymentDetailInfo = new BatchPaymentDetailInfo();
                                batchPaymentDetailInfo.setBusinessNo(ObjectUtil.isNotEmpty(item.getIdClmPaymentItemFee())
                                        ? item.getIdClmPaymentItemFee() : item.getIdClmPaymentItem());
                                batchPaymentDetailInfo.setCurrency(item.getPaymentCurrencyCode());
                                batchPaymentDetailInfo.setPlanFee(item.getCoinsuranceActualAmount());
                                List<ClmsPaymentPlan> clmsPaymentPlanList = clmsPaymentPlanService.getClmsPaymentPlanList(ObjectUtil.isNotEmpty(item.getIdClmPaymentItemFee())
                                        ? item.getIdClmPaymentItemFee() : item.getIdClmPaymentItem());
                                ClmsPaymentPlan clmsPaymentPlan = ObjectUtil.isNotEmpty(clmsPaymentPlanList)
                                        ? clmsPaymentPlanList.get(0) : new ClmsPaymentPlan();
                                batchPaymentDetailInfo.setRiskCode(clmsPaymentPlan.getPlanCode());
                                if ((ObjectUtil.isEmpty(planTaxMap.get(clmsPaymentPlan.getPlanCode()))
                                        || 0 == planTaxMap.get(clmsPaymentPlan.getPlanCode())) || !finalTaxFlag) {
                                    batchPaymentDetailInfo.setTaxFee(BigDecimal.ZERO);
                                } else {
                                    batchPaymentDetailInfo.setTaxFee(item.getCoinsuranceActualTax());
                                }
                                batchPaymentDetailInfo.setKindCode(clmsPaymentPlan.getKindCode());
                                batchPaymentDetailInfo.setProductCode(clmsPaymentPlan.getProductCode());
                                batchPaymentDetailInfo.setProductLineCode(clmsPaymentPlan.getProductLineCode());
                                batchPaymentDetailInfo.setReportNo(finalReportNo);
                                batchPaymentDetailInfo.setCaseTimes(item.getCaseTimes());
                                batchPaymentDetailInfoList.add(batchPaymentDetailInfo);
                            });
                            List<InvoiceInfoDTO> invoiceInfoList = new ArrayList<>();
                            if (idList != null && idList.size() > 0) {
                                invoiceInfoList = feePayMapper.getInvoiceList(idList);
                            }
                            BatchPaymentInfo batchPaymentInfo = new BatchPaymentInfo();
                            //主信息
                            BatchPaymentMainInfo batchPaymentMainInfo = new BatchPaymentMainInfo();
                            String batchNo = JobServiceImpl.generateBatchNumber();
                            batchPaymentMainInfo.setBatchNo(batchNo);
                            batchPaymentMainInfo.setBatchType("3");
                            batchPaymentMainInfo.setSumCount(paymentItemDTOList.size());
                            if (invoiceInfoList != null && invoiceInfoList.size() > 0) {
                                if("000".equals(invoiceType)){
                                    batchPaymentMainInfo.setIsExistInvoice("0");
                                }else{
                                    batchPaymentMainInfo.setIsExistInvoice("1");
                                }
                            } else {
                                batchPaymentMainInfo.setIsExistInvoice("0");
                            }
                            batchPaymentMainInfo.setSumAmount(sumAmount);
                            //发票退回接口
                            batchPaymentMainInfo.setInvoiceReturnURL(invoiceReturnURL);
                            batchPaymentMainInfo.setPayStatusReturnURL(paymentBackURL + "/public/pay/mergePaymentBackResult");

                            batchPaymentMainInfo.setParterCode(v.getCustomerNo());
                            batchPaymentMainInfo.setParterName(v.getClientName());

                            batchPaymentInfo.setBatchPaymentMainInfo(batchPaymentMainInfo);
                            batchPaymentInfo.setBatchPaymentDetailInfo(batchPaymentDetailInfoList);

                            List<BatchPaymentInvoice> batchPaymentInvoiceList = new ArrayList<>();
                            if (invoiceInfoList != null && invoiceInfoList.size() > 0) {
                                invoiceInfoList.stream()
                                        .forEach(invoice -> {
                                            BatchPaymentInvoice batchPaymentInvoice = new BatchPaymentInvoice();
                                            batchPaymentInvoice.setSumAmount(invoice.getTotalAmount());
                                            batchPaymentInvoice.setAmount(invoice.getNoTaxAmount());
                                            batchPaymentInvoice.setTaxAmount(invoice.getTaxAmount());
                                            batchPaymentInvoice.setInvoiceType(invoice.getInvoiceType());
                                            batchPaymentInvoice.setInvoiceCode(invoice.getInvoiceCode());
                                            batchPaymentInvoice.setInvoiceNo(invoice.getInvoiceNo());
                                            batchPaymentInvoice.setInvoiceDate(invoice.getInvoiceDate());
                                            batchPaymentInvoice.setInvoiceView(invoice.getFileId());
                                            BigDecimal taxRate = ObjectUtil.isNotEmpty(invoice.getTaxRate())
                                                    ? new BigDecimal(invoice.getTaxRate()).divide(new BigDecimal(100))
                                                    : BigDecimal.ZERO;
                                            batchPaymentInvoice.setTaxRate(taxRate);
                                            batchPaymentInvoiceList.add(batchPaymentInvoice);
                                        });
                            }
                            batchPaymentInfo.setInvoiceList(batchPaymentInvoiceList);

                            //将主信息存数据库
                            log.info("送理赔批量接口入参：" + JsonUtils.toJsonString(batchPaymentInfo));
                            MergePaymentDTO mergePaymentDTO = new MergePaymentDTO();
                            jobService.bulidMergePaymentDTO(batchPaymentMainInfo, mergePaymentDTO);
                            paymentItemMapper.addClmsMergePayment(mergePaymentDTO);
                            int id = mergePaymentDTO.getId();
                            //更新clm_payment_item表
                            paymentItemMapper.updatePaymentItemIdByItem(id,v.getIdClmPaymentItem());
                            //送收付
                            if (batchPaymentMainInfo != null) {
                                this.sendMergePayment(batchNo, batchPaymentInfo, batchPaymentDetailInfoList);
                            }
//                        }
                    }
                }
                returnList.addAll(paymentItemDTOList);
            });
            LogUtil.info("理赔批量支付结束执行：");
            return returnList;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 理赔费用发票 送收付Q13
     *
     * @param paymentItemDTO
     */
    @Override
    public void sendClaimVatInfo(PaymentItemDTO paymentItemDTO) {
        if (PaymentTypeEnum.FEE.getType().equals(paymentItemDTO.getPaymentType())
                || PaymentTypeEnum.PRE_FEE.getType().equals(paymentItemDTO.getPaymentType())) {
            sendClaimVatInfo(paymentItemDTO.getIdClmPaymentItem(), paymentItemDTO.getReportNo(), paymentItemDTO.getPolicyNo(),false);
        }
        if (PaymentTypeEnum.REPLEVY_FEE.getType().equals(paymentItemDTO.getPaymentType())){
            sendClaimVatInfo(paymentItemDTO.getIdClmPaymentItem(), paymentItemDTO.getReportNo(), paymentItemDTO.getPolicyNo(),true);
        }
    }

    /**
      *
      * @Description 送收付 及调用接口异常补偿
     * orderPayment:指令支付标记
      * <AUTHOR>
      * @Date 2023/6/13 14:18
      **/
    private void sendPayment(String paySerialNo, List<PaymentItemDTO> itemDTO, ClaimSettlementPayment claimSettlementPayment,boolean isUpdate) {
       log.info("sendPayment准备调用支付接口了paySerialNo={}",paySerialNo);
        PayInfoNotice<ClaimSettlementPayment> payInfoNotice = new PayInfoNotice<>();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        if (StringUtils.isNotEmpty(paySerialNo)) {
            payInfoHead.setRequestType(PayInfoHead.TYPE_Q09);
        } else {
            payInfoHead.setRequestType(PayInfoHead.TYPE_Q02);
        }
        payInfoHead.setUserCode(userCode);
        payInfoNotice.setHead(payInfoHead);
        payInfoNotice.setBody(claimSettlementPayment);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        log.info("noticePayment-payInfoNotice:" + JSON.toJSONString(payInfoNotice));
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        String status = "0";
        log.info("sendPayment准备调用支付接口开始加签paySerialNo={}",JsonUtils.toJsonString(claimSettlementPayment));
        // 加签
        try {
            if(skipCase.contains(claimSettlementPayment.getAdjustMaiInfo().getClaimNo())){
                log.warn("跳过实际推送收付，reportNo：{}", claimSettlementPayment.getAdjustMaiInfo().getClaimNo());
            }else {
                log.info("noticePayment-payInfoNotice-payUrl:" + payUrl);
                String result;
                if (switchMesh) {
                    log.info("调用支付接口用mesh方式");
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json;charset:utf-8");
                    result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice), headers);
                } else {
                    result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
                }

                if (StringUtils.isNotEmpty(result)) {
                    JSONObject jsonObject = JSON.parseObject(result);
                    log.info("noticePayment-success:" + jsonObject);
                    payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                    if (!payResult.isSuccess(payResult.getResponseCode())) {
                        log.info("noticePayment-fail: 收付费返回失败");
                        log.info("noticePayment-fail-errorCode: " + payResult.getErrorCode());
                        log.info("noticePayment-fail-errorMessage: " + payResult.getErrorMessage());
                        status = "1";
                    }
                } else {
                    log.info("noticePayment-fail: 调用接口异常");
                    status = "1";
                }
            }
        } catch (Exception e) {
            log.error("收付费接口调用异常", e);
            status= "1";
        }
        // 重开冲销的不用更新
        if (isUpdate){
            List<AdjustFee> adjustFee = claimSettlementPayment.getAdjustFee();
            Map<String, List<AdjustFee>> itemGroupByPolicyNo = adjustFee.stream().collect(Collectors.groupingBy(AdjustFee::getPaySerialNo));
            String finalStatus = status;
            itemDTO.forEach(item->{
                // 送收付金额为0的直接更新成支付完成
                List<AdjustFee> adjustFees = itemGroupByPolicyNo.get(item.getIdClmPaymentItem());
                List<AdjustFee> adjustSonFees = itemGroupByPolicyNo.get(item.getIdClmPaymentItemFee());
                if ((CollectionUtils.isEmpty(adjustFees) || adjustFees.get(0).getSumPaid() == 0)
                        && (CollectionUtils.isEmpty(adjustSonFees) || adjustSonFees.get(0).getSumPaid() == 0)){
                    item.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_80);
                } else{
                    if(SettleConst.CLAIM_TYPE_REP_PAY.equals(item.getClaimType())&&"1".equals(finalStatus)){
                        throw new GlobalBusinessException("送收付失败");
                    }
                    item.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_11);
                }
                paymentItemMapper.updatePaymentItem(item);
            });
        }
        // 存送收付记录
        SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
        sendPaymentRecord.setReportNo(claimSettlementPayment.getAdjustMaiInfo().getClaimNo());
        sendPaymentRecord.setRemark("");
        try {
            if(SettleConst.CLAIM_TYPE_REP_PAY.equals(itemDTO.get(0).getClaimType())){
                int length = claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo().length();
                sendPaymentRecord.setCaseTimes(Integer.valueOf(claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo().substring(length - 1)));
                sendPaymentRecord.setRemark("追偿" + claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo());
            }else{
                sendPaymentRecord.setCaseTimes(Integer.valueOf(claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo()));
            }
        }catch (Exception e){
            log.warn("保存推送收付记录转换赔付次数异常，LossSeqNo：{}", claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo(), e);
            if(StringUtils.isNotEmpty(claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo())){
                //”Y“开头为预赔单
                int length = claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo().length();
                sendPaymentRecord.setCaseTimes(Integer.valueOf(claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo().substring(length - 1)));
                sendPaymentRecord.setRemark("预赔单" + claimSettlementPayment.getAdjustMaiInfo().getLossSeqNo());
            }
        }
        sendPaymentRecord.setPaySerialNo(StringUtils.cancelNull(paySerialNo));
        sendPaymentRecord.setRequestType(payInfoHead.getRequestType());
        sendPaymentRecord.setRequestTypeDesc(payInfoHead.getRequestType());
        sendPaymentRecord.setRequestParam(JSON.toJSONString(payInfoNotice));
        sendPaymentRecord.setResponseParam(payResult.getErrorMessage());
        sendPaymentRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
        sendPaymentRecordMapper.insertSelective(sendPaymentRecord);
        Long sendPaymentRecordId = sendPaymentRecord.getId();

        //补偿处理
        if ("1".equals(status)){
            String reportNo = claimSettlementPayment.getAdjustMaiInfo().getClaimNo();
            asynchronousCompensation(reportNo,payInfoNotice, payResult, status,sendPaymentRecordId+"");
        }
    }

    /**
     * @Description 合并支付送收付
     **/
    public void sendMergePayment(String batchNo, BatchPaymentInfo batchPaymentInfo,
                                  List<BatchPaymentDetailInfo> batchPaymentDetailInfo) {
        log.info("sendMergePayment准备调用支付接口了:{}",batchNo);
        PayInfoNotice<BatchPaymentInfo> payInfoNotice = new PayInfoNotice<>();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        payInfoHead.setRequestType(PayInfoHead.TYPE_Q18);
        payInfoHead.setUserCode(userCode);
        payInfoNotice.setHead(payInfoHead);
        payInfoNotice.setBody(batchPaymentInfo);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        log.info("sendMergePayment-payInfoNotice:" + JSON.toJSONString(payInfoNotice));
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        String status = "0";
        log.info("sendMergePayment准备调用支付接口开始加签",JsonUtils.toJsonString(batchPaymentInfo));
        // 加签
        try {
            log.info("sendMergePayment-payInfoNotice-payUrl:" + payUrl);
            String result;
            if (switchMesh) {
                log.info("sendMergePayment-调用支付接口用mesh方式");
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json;charset:utf-8");
                result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice), headers);
            } else {
                result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
            }

            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("sendMergePayment-success:" + jsonObject);
                payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                if (!payResult.isSuccess(payResult.getResponseCode())) {
                    log.info("sendMergePayment-fail: 收付费返回失败");
                    log.error("sendMergePayment-fail-errorCode: " + payResult.getErrorCode());
                    log.error("sendMergePayment-fail-errorMessage: " + payResult.getErrorMessage() + "打包批次号：{}" + batchNo);
                    status = "1";
                }
            } else {
                log.info("sendMergePayment-fail: 调用接口异常");
                status = "1";
            }
        } catch (Exception e) {
            log.error("sendMergePayment收付费接口调用异常", e);
            status= "1";
        }
        for(BatchPaymentDetailInfo info: batchPaymentDetailInfo){
            // 存送收付记录
            SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
            sendPaymentRecord.setCreatedBy("SYSTEM");
            sendPaymentRecord.setUpdatedBy("SYSTEM");
            sendPaymentRecord.setCreatedDate(new Date());
            sendPaymentRecord.setUpdatedDate(new Date());
            sendPaymentRecord.setReportNo(info.getReportNo());
            sendPaymentRecord.setRemark("");
            sendPaymentRecord.setCaseTimes(info.getCaseTimes());
            sendPaymentRecord.setPaySerialNo(info.getBusinessNo());
            sendPaymentRecord.setRequestType(payInfoHead.getRequestType());
            sendPaymentRecord.setRequestTypeDesc(payInfoHead.getRequestType());
            sendPaymentRecord.setRequestParam("");
            sendPaymentRecord.setResponseParam("");
            sendPaymentRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
            sendPaymentRecordMapper.insertSelective(sendPaymentRecord);
        }
        //上述不存报文，合并支付同批次的报文存clms_send_merge_payment_record表
        SendMergePaymentRecordDTO sendMergePaymentRecordDTO = new SendMergePaymentRecordDTO();
        sendMergePaymentRecordDTO.setCreatedBy("SYSTEM");
        sendMergePaymentRecordDTO.setUpdatedBy("SYSTEM");
        sendMergePaymentRecordDTO.setBatchNo(batchNo);
        sendMergePaymentRecordDTO.setRequestParam(JSON.toJSONString(payInfoNotice));
        sendMergePaymentRecordDTO.setResponseParam(payResult.getErrorMessage());
        sendMergePaymentRecordDTO.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);

        sendPaymentRecordMapper.insertSendMergePaymentRecord(sendMergePaymentRecordDTO);
        //更新主信息batchPaymentMainInfo merge_payment_status 为送11-收付成功 一开始为10草稿
        if("0".equals(status)){
            String mergePaymentStatus = "11";
            paymentItemMapper.updateMergePaymentStatus(batchNo,mergePaymentStatus);
        }

    }

    /**
      *
      * @Description  价税分离接口调用送收付
      * <AUTHOR>
      * @Date 2023/6/28 10:50
      **/
    private void sendClaimVatInfo(String paySerialNo, String reportNo, String policyNo,Boolean isReplevy) {
        log.info("noticePayment-sendClaimVatInfo-start,paySerialNo:{}, reportNo:{}",paySerialNo,reportNo);
        PayInfoNotice<ClaimSendVatInfo> payInfoNotice = new PayInfoNotice<>();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        payInfoHead.setRequestType(PayInfoHead.TYPE_Q13);
        payInfoHead.setUserCode(userCode);
        payInfoNotice.setHead(payInfoHead);
        ClaimVatInfo claimVatInfo = new ClaimVatInfo();
        claimVatInfo.setPaySerialNo(paySerialNo);
        claimVatInfo.setInvoiceReturnURL(invoiceReturnURL);
        //查询险种是否含税
        Map<String, Boolean> planTaxRateMap = productService.getPlanTaxRate(paySerialNo);
        List<InvoiceTypeTaxInfoDTO> invoiceTypeList = new ArrayList<>();
        if(isReplevy){
            invoiceTypeList = feePayMapper.getReplevyInvoiceTypeList(paySerialNo);
        }else{
            invoiceTypeList = feePayMapper.getInvoiceTypeList(paySerialNo);
        }
        if (CollectionUtils.isEmpty(invoiceTypeList)){
            log.error("价税分离接口未查到费用信息,流水号入参:{}",paySerialNo);
            return;
        } else if ("000".equals(invoiceTypeList.get(0).getInvoiceType())) {//发票类型为无发票不调用接口
            log.error("价税分离接口查询发票类型为无发票,流水号入参::{}",paySerialNo);
            return;
        }
        log.info("查询险种是否含税,planTaxRateMap:{}",JSON.toJSONString(planTaxRateMap));
        //普票不计税，专票计税
        invoiceTypeList = invoiceTypeList.stream().filter(taxInfoDTO -> InvoiceTypeEnum.isExistSpecialInvoice(taxInfoDTO.getInvoiceType())).collect(Collectors.toList());

        List<ClmsPaymentPlan> clmsPaymentPlanList = clmsPaymentPlanService.getClmsPaymentPlanList(paySerialNo);
        if (CollectionUtils.isEmpty(clmsPaymentPlanList)){
            log.error("价税分离接口未查到clmsPaymentPlanList信息,流水号入参:{}",paySerialNo);
            return;
        }
        log.info("查询险种支付信息,clmsPaymentPlanList:{}",JSON.toJSONString(clmsPaymentPlanList));
        //过滤出险种不免税的数据
        List<ClmsPaymentPlan> newPaymentPlanList = new ArrayList<>();
        List<ClmsPaymentPlan> otherPaymentPlanList = new ArrayList<>();
        for (ClmsPaymentPlan paymentPlan : clmsPaymentPlanList){
            if (paymentPlan.getPlanPayAmount() != null && paymentPlan.getPlanPayAmount().compareTo(BigDecimal.ZERO) > 0) {
                if (planTaxRateMap.containsKey(paymentPlan.getPlanCode()) && planTaxRateMap.get(paymentPlan.getPlanCode())) {
                    newPaymentPlanList.add(paymentPlan);
                } else {
                    otherPaymentPlanList.add(paymentPlan);
                }
            }
        }
        int invoiceTypeCount = invoiceTypeList.size();
        //当获赔责任所属条款为应税时，且理赔费用对应的发票为专票，送收付理赔费用按照发票录入费率进行价税拆分
        //当责任对应条款为免税时，或当理赔费用发票为普票时，送收付费用不需要进行价税拆分
        List<RiskTax> riskTaxList = new ArrayList<>();
        log.info("过滤险种应税数据, 发票类型数量:{}, clmsPaymentPlanList:{},otherPaymentPlanList:{}, newPaymentPlanList:{}"
                ,invoiceTypeCount, JSON.toJSONString(newPaymentPlanList),JSON.toJSONString(otherPaymentPlanList),JSON.toJSONString(newPaymentPlanList));
        for (ClmsPaymentPlan clmsPaymentPlan : newPaymentPlanList) {
            //如果险种应税，但不是专票，则税额设置为0
            if (invoiceTypeCount == 0){
                RiskTax riskTax = new RiskTax();
                riskTax.setRiskCode(clmsPaymentPlan.getPlanCode());
                riskTax.setTaxAmount(new BigDecimal(0));
                riskTaxList.add(riskTax);
                continue;
            }
            // 险种税额累计金额
            BigDecimal planTaxAmountSum = BigDecimal.ZERO;
            for (int i = 0; i < invoiceTypeCount; i++){
                BigDecimal planTaxAmount ;
                //如果是最后一个进行轧差
                if (i == (invoiceTypeCount - 1)){
                    planTaxAmount = clmsPaymentPlan.getTaxAmount().subtract(planTaxAmountSum);
                }else {
                    planTaxAmount = clmsPaymentPlan.getTaxAmount().divide(new BigDecimal(Integer.valueOf(invoiceTypeCount)), 2, RoundingMode.HALF_UP);
                    planTaxAmountSum = planTaxAmountSum.add(planTaxAmount);
                }
                // 不在判断抄单的税率与发票的税额,理赔所有费用都送收付Q13。
                RiskTax riskTax = new RiskTax();
                riskTax.setRiskCode(clmsPaymentPlan.getPlanCode());
                riskTax.setTaxAmount(planTaxAmount);
                riskTaxList.add(riskTax);
            }
        }
        //险种免税或不为专票，则不计税，设置为0
        for (ClmsPaymentPlan clmsPaymentPlan :otherPaymentPlanList){
            RiskTax riskTax = new RiskTax();
            riskTax.setRiskCode(clmsPaymentPlan.getPlanCode());
            riskTax.setTaxAmount(new BigDecimal(0));
            riskTaxList.add(riskTax);
        }
        //免税也要送发票
        /*if (CollectionUtils.isEmpty(riskTaxList)) {
            log.info("noticePayment-sendClaimVatInfo-没有含税金额:" + paySerialNo);
            return;
        }*/
        log.info("组装险种应税数据, riskTaxList:{}", JSON.toJSONString(riskTaxList));
        claimVatInfo.setRiskTaxList(riskTaxList);
        List<Invoice> invoiceList = new ArrayList<>();
        int caseTimes = 1;
        if(isReplevy){
            ClmsReplevyCharge replevyChargeVo = clmsReplevyChargeMapper.getReplevyChargeByIdClmPaymentItem(paySerialNo);
            if(replevyChargeVo!=null) {
                InvoiceInfoDTO invoiceInfo = feePayMapper.getInvoiceInfoById(replevyChargeVo.getId());
                Invoice invoice = new Invoice();
                invoice.setInvoiceCode(invoiceInfo.getInvoiceCode());
                invoice.setInvoiceNo(invoiceInfo.getInvoiceNo());
                invoice.setInvoiceType(invoiceInfo.getInvoiceType());
                invoice.setInvoiceView(invoiceInfo.getFileId());
                invoice.setSumAmount(invoiceInfo.getTotalAmount());
                invoice.setAmount(invoiceInfo.getNoTaxAmount());
                invoice.setTaxAmount(invoiceInfo.getTaxAmount());
                invoice.setInvoiceDate(invoiceInfo.getInvoiceDate());
                if (null != invoiceInfo.getTaxRate()) {
                    invoice.setTaxRate(new BigDecimal(invoiceInfo.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                }
                invoiceList.add(invoice);
                caseTimes =replevyChargeVo.getSerialNo();
            }
        }else{
            List<FeeInfoDTO> feeInfoDTOList = feePayMapper.getFeePayByIdClmPaymentInfo(paySerialNo);
            if (CollectionUtils.isEmpty(feeInfoDTOList)){
                log.info("noticePayment-sendClaimVatInfo-没有查到有效发票:" + paySerialNo);
                return;
            }
            boolean isMainCoinsFlag = coinsureService.isMainCoinsureFlag(reportNo);
            feeInfoDTOList.forEach(feeInfoDTO -> {
                InvoiceInfoDTO invoiceInfo = new InvoiceInfoDTO();
                invoiceInfo.setIdAhcsFeePay(feeInfoDTO.getIdAhcsFeePay());
                invoiceInfo = feePayMapper.getInvoiceInfo(invoiceInfo);
                BigDecimal totalAmount = BigDecimal.ZERO;
                BigDecimal noTaxAmount = BigDecimal.ZERO;
                BigDecimal taxAmount = BigDecimal.ZERO;
//                仅支付我司份额的时候，发票传收付页面录入多少就传多少
//                if(isMainCoinsFlag){
//                    PaymentItemDTO paymentItemDTO = paymentItemMapper.selectByItemId(feeInfoDTO.getIdClmPaymentItem());
//                    List<CoinsureDTO> coinsureDTOS = coinsureService.getCoinsureByPolicyNo(policyNo);
//                    CoinsureDTO coinsureDTO = coinsureDTOS.stream().filter(coins->paymentItemDTO.getCoinsuranceCompanyCode().equals(coins.getReinsureCompanyCode())).findFirst().orElse(null);
//                    BigDecimal coinsRate = coinsureDTO.getReinsureScale().divide(new BigDecimal(100)).setScale(2,RoundingMode.HALF_UP);
//                    totalAmount = paymentItemDTO.getCoinsuranceActualAmount();
//                    taxAmount = invoiceInfo.getTaxAmount().multiply(coinsRate).setScale(2,RoundingMode.HALF_UP);
//                    noTaxAmount = totalAmount.subtract(taxAmount);
//                }else{
                    totalAmount = invoiceInfo.getTotalAmount();
                    taxAmount = invoiceInfo.getTaxAmount();
                    noTaxAmount = invoiceInfo.getNoTaxAmount();
//                }

                Invoice invoice = new Invoice();
                invoice.setInvoiceCode(invoiceInfo.getInvoiceCode());
                invoice.setInvoiceNo(invoiceInfo.getInvoiceNo());
                invoice.setInvoiceType(invoiceInfo.getInvoiceType());
                invoice.setInvoiceView(invoiceInfo.getFileId());
                invoice.setSumAmount(totalAmount);
                invoice.setAmount(noTaxAmount);
                invoice.setTaxAmount(taxAmount);
                invoice.setInvoiceDate(invoiceInfo.getInvoiceDate());
                if (null != invoiceInfo.getTaxRate()) {
                    invoice.setTaxRate(new BigDecimal(invoiceInfo.getTaxRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
                }
                invoiceList.add(invoice);
            });
            caseTimes =feeInfoDTOList.get(0).getCaseTimes();
        }

        claimVatInfo.setInvoiceList(invoiceList);
        ClaimSendVatInfo claimSendVatInfo = new ClaimSendVatInfo();
        claimSendVatInfo.setClaimVatInfo(claimVatInfo);
        payInfoNotice.setBody(claimSendVatInfo);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        String status = "0";
        String result = null;
        String requestParam = JSON.toJSONString(payInfoNotice);
        log.info("noticePayment-sendClaimVatInfo:{}" ,requestParam);
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        // 加签
        try {
            log.info("noticePayment-sendClaimVatInfo-payUrl:" + payUrl);
            if (switchMesh){
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json;charset:utf-8");
                result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), requestParam,headers);
            }else {
                result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
            }
            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("noticePayment-sendClaimVatInfo-success:{}",jsonObject);
                payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                if (!payResult.isSuccess(payResult.getResponseCode())) {
                    log.info("noticePayment-sendClaimVatInfo-fail: 收付费返回失败");
                    log.info("noticePayment-sendClaimVatInfo-fail-errorCode: " + payResult.getErrorCode());
                    log.info("noticePayment-sendClaimVatInfo-fail-errorMessage: " + payResult.getErrorMessage());
                    status = "1";
                }
            } else {
                log.info("noticePayment-sendClaimVatInfo-fail: 调用接口异常");
                status = "1";
            }
        } catch (Exception e) {
            log.error("收付费接口调用异常", e);
            status = "1";
        }
        // 存送再保记录
        SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
        sendPaymentRecord.setReportNo(reportNo);
        sendPaymentRecord.setCaseTimes(caseTimes);
        sendPaymentRecord.setPaySerialNo(paySerialNo);
        sendPaymentRecord.setRequestType(PayInfoHead.TYPE_Q13);
        sendPaymentRecord.setRequestTypeDesc("价税分离");
        sendPaymentRecord.setRequestParam(requestParam);
        sendPaymentRecord.setResponseParam(StringUtils.cancelNull(result));
        sendPaymentRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
        if(isReplevy){
            sendPaymentRecord.setRemark("追偿费用");
        }else{
            sendPaymentRecord.setRemark("");
        }
        sendPaymentRecordMapper.insertSelective(sendPaymentRecord);
        Long sendPaymentRecordId = sendPaymentRecord.getId();

        //补偿处理
        if ("1".equals(status)) {
            asynchronousCompensation(reportNo,payInfoNotice, payResult, status,sendPaymentRecordId+"");
        }
        log.info("noticePayment-sendClaimVatInfo-end");
    }

    /**
      *
      * @Description 前次有赔付 本次无赔付的特殊处理
      * <AUTHOR>
      * @Date 2023/6/13 14:19
      **/
    /*private void handleLastPayButThisNot(String reportNo, Integer caseTimes, List<PaymentItemDTO> allItems,ReportCustomerInfoEntity customerInfo) {
        // 筛选出本次赔付的保单列表
        List<String> policyNos = allItems.stream().filter(item -> PaymentTypeEnum.PAY.getType().equals(item.getPaymentType()))
                .map(PaymentItemDTO::getPolicyNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(policyNos)){
            return;
        }
        List<PaymentItemDTO> paymentItemDTOS = new ArrayList<>();
        // 倒序找前一次赔付不为零的记录
        for (int i = caseTimes - 1; i > 0; i--) {
            PaymentItemDTO query = new PaymentItemDTO();
            query.setReportNo(reportNo);
            query.setCaseTimes(i);
            List<PaymentItemDTO> paymentItem = paymentItemMapper.getPaymentItem(query);
            List<PaymentItemDTO> collect = paymentItem.stream().filter(item -> PaymentTypeEnum.PAY.getType().equals(item.getPaymentType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collect)){
                paymentItemDTOS.addAll(collect);
                break;
            }
        }
        if (CollectionUtils.isEmpty(paymentItemDTOS)){
            return;
        }
        // 判断是否 前次有赔付 但本次无赔付的赔付信息
        List<PaymentItemDTO> itemDTOS = paymentItemDTOS.stream().filter(item -> !policyNos.contains(item.getPolicyNo())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemDTOS)){
            return;
        }
        Map<String, List<PaymentItemDTO>> itemGroupByPolicyNo = allItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getPolicyNo));
        itemGroupByPolicyNo.forEach((k, v) -> {
            PaymentItemDTO itemDTO = v.get(0);
            ClaimSettlementPayment claimSettlementPayment = new ClaimSettlementPayment();
            PolicyMainInfo policyMainInfo = getPolicyMainInfo(customerInfo, k);
            claimSettlementPayment.setPolicyMainInfo(policyMainInfo);
            AdjustMaiInfo adjustMaiInfo = getAdjustMaiInfo(k, itemDTO, false);
            claimSettlementPayment.setAdjustMaiInfo(adjustMaiInfo);
            List<AdjustFee> adjustFees = getAdjustFeesForElimination(v);
            claimSettlementPayment.setAdjustFee(adjustFees);
            // 接口调用需要的参数
            sendPayment(null, v, claimSettlementPayment,false);
        });
    }

    private List<AdjustFee> getAdjustFeesForElimination(List<PaymentItemDTO> paymentItems) {
        List<AdjustFee> adjustFees = new ArrayList<>();
        paymentItems.forEach(p->{
            AdjustFee adjustFee = new AdjustFee();
            String idClmPaymentItem = p.getIdClmPaymentItem();
            adjustFee.setTransType(AdjustFee.TRANS_TYPE_FIRST);
            adjustFee.setPaySerialNo(idClmPaymentItem);
            adjustFee.setPayee(p.getClientName());
            adjustFee.setCurrency("CNY");
            adjustFee.setPaidMethodCode(p.getCollectPayApproach());
            adjustFee.setSumPaid(p.getPaymentAmount().doubleValue());
            adjustFee.setRemark("");
            adjustFee.setFeeTypeCode(PaymentTypeEnum.getCode(p.getPaymentType()));
            adjustFee.setAccountNo(p.getClientBankAccount());
            adjustFee.setPayeeBankAccountName(p.getClientName());
            String clientBankCode = p.getClientBankCode();
            String clientBankName = p.getClientBankName();
            if(null == clientBankCode){
                clientBankCode = commonParameterMapper.getBankCodeByName(clientBankName);
            }
            adjustFee.setBankPayType(p.getBankAccountAttribute());
            adjustFee.setBankCode(clientBankCode);
            adjustFee.setBankName(clientBankName);
            // 收款方银行联行号
            adjustFee.setPayeeBankCode(p.getBankDetailCode());
            adjustFee.setPayeeBankName(p.getBankDetail());
            List<AdjustmentFeeDetail> adjustmentFeeDetails = new ArrayList<>();
            List<ClmsPaymentPlan> clmsPaymentPlanList = clmsPaymentPlanService.getClmsPaymentPlanList(idClmPaymentItem);
            if (!CollectionUtils.isEmpty(clmsPaymentPlanList)){
                for (ClmsPaymentPlan clmsPaymentPlan : clmsPaymentPlanList) {
                    AdjustmentFeeDetail adjustmentFeeDetail = new AdjustmentFeeDetail();
                    adjustmentFeeDetail.setRiskCode(clmsPaymentPlan.getPlanCode());
                    adjustmentFeeDetail.setFeeTypeCode("P60");
                    BigDecimal planPayAmount = clmsPaymentPlan.getPlanPayAmount();
                    // 取负数冲销
                    adjustmentFeeDetail.setPayAmount(planPayAmount.negate());
                    adjustmentFeeDetail.setKindCode(clmsPaymentPlan.getKindCode());
                    adjustmentFeeDetail.setProductCode(clmsPaymentPlan.getProductCode());
                    adjustmentFeeDetail.setProductLineCode(clmsPaymentPlan.getProductLineCode());
                    adjustmentFeeDetails.add(adjustmentFeeDetail);
                }
            }
            adjustFee.setAdjustmentFeeDetail(adjustmentFeeDetails);
            adjustFees.add(adjustFee);
        });
        return adjustFees;
    }*/

    private void asynchronousCompensation(String reportNo,PayInfoNotice payInfoNotice, PayResult payResult,
                                          String status,String sendPaymentRecordId) {
        AsynchronousCompensationJobExtDTO asynchronousCompensationJobDTO = new AsynchronousCompensationJobExtDTO();
        asynchronousCompensationJobDTO.setBusinessType("12");//12理赔送收付
        asynchronousCompensationJobDTO.setBusinessNo(reportNo);
        asynchronousCompensationJobDTO.setJobType("1");//1财务对接
        asynchronousCompensationJobDTO.setId(UuidUtil.getUUID());
        asynchronousCompensationJobDTO.setRequestParam(JSON.toJSONString(payInfoNotice));
        asynchronousCompensationJobDTO.setResponseParam(payResult.getErrorMessage());
        asynchronousCompensationJobDTO.setStatus(status);//1为失败
        asynchronousCompensationJobDTO.setRetryTimes(0);//重试次数
        asynchronousCompensationJobDTO.setSignature(printService.getSignature());
        asynchronousCompensationJobDTO.setUrl(payUrl);
        asynchronousCompensationJobDTO.setCreatedBy(ConstValues.SYSTEM_UM);
        asynchronousCompensationJobDTO.setCreatedDate(new Date());
        asynchronousCompensationJobDTO.setUpdatedBy(ConstValues.SYSTEM_UM);
        asynchronousCompensationJobDTO.setUpdatedDate(new Date());
        asynchronousCompensationJobDTO.setBusinessNoSub(sendPaymentRecordId);
        paymentItemMapper.insertJobInfo(asynchronousCompensationJobDTO);
    }

    /**
      *
      * @Description 组装领款人信息
      * <AUTHOR>
      * @Date 2023/4/29 11:00
      **/
    @NotNull
    private List<AdjustFee> getAdjustFees(String paySerialNo, List<PaymentItemDTO> paymentItems, boolean isVerifyPay, String reportSubMode) {
        List<AdjustFee> adjustFees = new ArrayList<>();
        paymentItems.forEach(p->{
            AdjustFee adjustFee = new AdjustFee();
            String idClmPaymentItem = p.getIdClmPaymentItem();
//            String paySerialNoEq = ObjectUtil.isNotEmpty(p.getIdClmPaymentItemFee()) ? p.getIdClmPaymentItemFee() : p.getIdClmPaymentItem();
            String payeeCode = "";
            PaymentInfoDTO paymentInfoById = paymentInfoMapper.getPaymentInfoById(p.getIdClmPaymentInfo());
            if(null != paymentInfoById && StringUtils.isNotEmpty(paymentInfoById.getCustomerNo())){
                payeeCode = paymentInfoById.getCustomerNo();
            }else {
                InsuredPersonDTO insuredPersonDTO = insuredPersonMapper.getInsuredPersonDTO(p.getReportNo());
                if(null != insuredPersonDTO){
                    payeeCode = insuredPersonDTO.getClientNo();
                }
            }
            if (idClmPaymentItem.equals(paySerialNo)){
                adjustFee.setTransType(AdjustFee.TRANS_TYPE_AGAIN);
            }else {
                adjustFee.setTransType(AdjustFee.TRANS_TYPE_FIRST);
            }
            //有子表主键则流水号用子表主键
            adjustFee.setPaySerialNo(ObjectUtil.isNotEmpty(p.getIdClmPaymentItemFee()) ? p.getIdClmPaymentItemFee() : idClmPaymentItem);
            adjustFee.setPayee(p.getClientName());
            adjustFee.setCurrency("CNY");
            // 通过报案号和赔付次数查询案件重开记录
            List<RestartCaseRecordEntity> restartCaseRecordEntityList=restartCaseService.getRestartCaseList(p.getReportNo(), p.getCaseTimes()-1);
            String restartReason="";
            if(!org.springframework.util.CollectionUtils.isEmpty(restartCaseRecordEntityList)){
                restartReason=restartCaseRecordEntityList.get(0).getRestartReason();
            }
            // 如果负数重开，支付方式为03-批量转账
            String collectPayApproach = p.getCollectPayApproach();
            if("7".equals(restartReason) || (org.springframework.util.StringUtils.hasText(collectPayApproach) && "215".equals(collectPayApproach))){
                adjustFee.setPaidMethodCode("03");
            }else{
                adjustFee.setPaidMethodCode(collectPayApproach);
            }
            adjustFee.setPayeeCode(payeeCode);
            // 微保传过来：1 微信零钱 2 银行转账 3-美团支付,  送收付需要把1、2转换为：1-资金支付，2-微信支付
            String lossPayWay ;
            if ("1".equals(p.getPayType())){
                // 1 微信零钱
                lossPayWay = "2";
            }else if ("3".equals(p.getPayType())){
                // 3-美团支付
                lossPayWay = p.getPayType();
            } else {
                // 2 银行转账
                lossPayWay = "1";
            }
            adjustFee.setLossPayWay(lossPayWay);
            if(Objects.equals("1",p.getPayType()) || Objects.equals("3",p.getPayType())){
                //微信零钱或美团点评 方式 需要传openid
                adjustFee.setOpenId(p.getOpenId());
            }
            //一步结案案件送备注给收付
            List<String> reportSubModeList = Arrays.asList(reportSubModeConfig.split(","));
            adjustFee.setRemark(reportSubModeList.contains(reportSubMode) ? p.getRemark() : "");
            adjustFee.setFeeTypeCode(PaymentTypeEnum.getCode(p.getPaymentType()));
            //判断是否有发票1-是，0-否，费用有发票，赔款无发票
            //赔款
            List<String> excludePaymentTypes = Arrays.asList("13","C13","11","P13");
            if(excludePaymentTypes.contains(p.getPaymentType())){
                adjustFee.setIsExistInvoice("0");
            } else {
                adjustFee.setIsExistInvoice("1");
                //费用类型若为无发票，则送收付类型为无发票，否则送收付类型为有发票
                List<FeeInfoDTO> feeInfoDTOList = feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem);
                if(feeInfoDTOList != null && feeInfoDTOList.size() > 0){
                    InvoiceInfoDTO invoiceInfo = new InvoiceInfoDTO();
                    invoiceInfo.setIdAhcsFeePay(feeInfoDTOList.get(0).getIdAhcsFeePay());
                    invoiceInfo = feePayMapper.getInvoiceInfo(invoiceInfo);
                    if(invoiceInfo != null){
                        if("000".equals(invoiceInfo.getInvoiceType())){
                            adjustFee.setIsExistInvoice("0");
                        }else{
                            adjustFee.setIsExistInvoice("1");
                        }
                    }
                }
            }
            adjustFee.setAccountNo(p.getClientBankAccount());
            adjustFee.setPayeeBankAccountName(p.getClientName());
            String clientBankCode = p.getClientBankCode();
            String clientBankName = p.getClientBankName();
            if(null == clientBankCode){
                clientBankCode = commonParameterMapper.getBankCodeByName(clientBankName);
            }
            adjustFee.setBankPayType(p.getBankAccountAttribute());
            adjustFee.setBankCode(clientBankCode);
            adjustFee.setBankName(clientBankName);
            // 收款方银行联行号
            adjustFee.setPayeeBankCode(p.getBankDetailCode());
            adjustFee.setPayeeBankName(p.getBankDetail());

            adjustFee.setCoinsCode(p.getCoinsuranceCompanyCode());
            adjustFee.setCoinsName(p.getCoinsuranceCompanyName());

            List<AdjustmentFeeDetail> adjustmentFeeDetails = new ArrayList<>();
            List<ClmsPaymentPlan> clmsPaymentPlanList = clmsPaymentPlanService.getClmsPaymentPlanList(ObjectUtil.isNotEmpty(p.getIdClmPaymentItemFee())?p.getIdClmPaymentItemFee():idClmPaymentItem);
            log.info("clmsPaymentPlanList的数据={}",JsonUtils.toJsonString(clmsPaymentPlanList));
            List<ClmsPaymentPlan> lastPaymentPlanList = new ArrayList<>();
            // 如果是重开的 需要减去前次赔付的钱
            if (p.getCaseTimes() > 1 && (PaymentTypeEnum.PAY.getType().equals(p.getPaymentType()) || PaymentTypeEnum.COIN_PAY.getType().equals(p.getPaymentType()))) {
                List<PaymentItemDTO> lastPayment = getLastPayment(p);
                if (!CollectionUtils.isEmpty(lastPayment)){
                    lastPaymentPlanList.addAll(clmsPaymentPlanService.getClmsPaymentPlanList(lastPayment.get(0).getIdClmPaymentItem()));
                }
            }
            // 如果案件有过预赔且本次是核赔非预赔的 需要减去所有的已预赔 重开不涉及预赔
            if (p.getCaseTimes() == 1 && isVerifyPay && (PaymentTypeEnum.PAY.getType().equals(p.getPaymentType()) || PaymentTypeEnum.COIN_PAY.getType().equals(p.getPaymentType()))) {
                List<PaymentItemDTO> prePayment = getAllPrePayment(p);
                if (!CollectionUtils.isEmpty(prePayment)){
                    for (int i = 0; i < prePayment.size(); i++) {
                        // 每一次的预赔赔付金额拆分逻辑
                        List<ClmsPaymentPlan> prepPaymentPlan = clmsPaymentPlanService.getClmsPaymentPlanList(prePayment.get(i).getIdClmPaymentItem());
                        if (i==0){
                            lastPaymentPlanList.addAll(prepPaymentPlan);
                        } else {
                            for (int j = 0; j < lastPaymentPlanList.size(); j++) {
                                lastPaymentPlanList.get(j).setPlanPayAmount(lastPaymentPlanList.get(j).getPlanPayAmount().add(prepPaymentPlan.get(j).getPlanPayAmount()));
                            }
                        }
                    }
                }
            }
            log.info("lastPaymentPlanList的值= {}",JsonUtils.toJsonString(lastPaymentPlanList));
            if (!CollectionUtils.isEmpty(clmsPaymentPlanList)){
                BigDecimal sumPay = BigDecimal.ZERO;
                for (int i = 0; i < clmsPaymentPlanList.size(); i++) {
                    ClmsPaymentPlan clmsPaymentPlan = clmsPaymentPlanList.get(i);
                    AdjustmentFeeDetail adjustmentFeeDetail = new AdjustmentFeeDetail();//费用险种级别
                    adjustmentFeeDetail.setRiskCode(clmsPaymentPlan.getPlanCode());
                    BigDecimal planPayAmount = clmsPaymentPlan.getPlanPayAmount();
                    BigDecimal taxRate = policyPlanMapper.getTaxRate(p.getReportNo(),clmsPaymentPlan.getPlanCode());
                    if(ObjectUtil.isNotEmpty(taxRate)){
                        taxRate = taxRate.setScale(2,RoundingMode.HALF_UP);
                        adjustmentFeeDetail.setVatRate(taxRate);
                    }
                    if ("P61".equals(adjustFee.getFeeTypeCode()) || "P90".equals(adjustFee.getFeeTypeCode())){
                        List<FeeInfoDTO> feeInfoDTOList=  feePayMapper.getFeePayByIdClmPaymentInfo(idClmPaymentItem);
                        adjustmentFeeDetail.setFeeTypeCode(ChargeCodeEnum.getCode(feeInfoDTOList.get(0).getFeeType()));
//                        planPayAmount = p.getCoinsuranceActualAmount();
                    } else {
                        adjustmentFeeDetail.setFeeTypeCode("P60");
                    }

                    BigDecimal chgPlanPayAmount = clmsPaymentPlan.getChgPlanPayAmount();
                    // 非重开并且险种分摊金额变化量为null时，执行之前逻辑（有预赔需要减去）
                    if(chgPlanPayAmount == null || p.getCaseTimes()== 1){
                        if (!CollectionUtils.isEmpty(lastPaymentPlanList)){
                            planPayAmount = planPayAmount.subtract(lastPaymentPlanList.get(i).getPlanPayAmount());
                        }
                        if (planPayAmount.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        sumPay = sumPay.add(planPayAmount );
                        adjustmentFeeDetail.setPayAmount(planPayAmount);
                    }else{
                        if (chgPlanPayAmount.compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                        sumPay = sumPay.add(chgPlanPayAmount );
                        adjustmentFeeDetail.setPayAmount(chgPlanPayAmount);
                    }
                    adjustmentFeeDetail.setKindCode(clmsPaymentPlan.getKindCode());
                    adjustmentFeeDetail.setProductCode(clmsPaymentPlan.getProductCode());
                    adjustmentFeeDetail.setProductLineCode(clmsPaymentPlan.getProductLineCode());
                    adjustmentFeeDetails.add(adjustmentFeeDetail);
                }
                adjustFee.setSumPaid(sumPay.doubleValue());
                p.setFinancePaymentAmount(sumPay);
            } else {
                p.setFinancePaymentAmount(BigDecimal.ZERO);
            }
            // 10月11日修改 原先是根据保单总金额判断大于0送收付，目前改成只要有金额变化就送收付
            if (!CollectionUtils.isEmpty(adjustmentFeeDetails)) {
                adjustFee.setAdjustmentFeeDetail(adjustmentFeeDetails);
                adjustFees.add(adjustFee);
            } else{
                log.info("不送收付的直接更新支付成功paySerialNo={}",paySerialNo);
                // 不送收付的直接更新支付成功
                p.setPaymentItemStatus(Constants.PAYMENT_ITEM_STATUS_80);
            }
            paymentItemMapper.updatePaymentItem(p);
        });
        adjustFees.stream()
                .filter(fee -> "215".equals(fee.getPaidMethodCode()))
                .forEach(fee -> fee.setPaidMethodCode("03"));
        log.info("adjustFees的值是adjustFees={}",JsonUtils.toJsonString(adjustFees));
        return adjustFees;
    }

    /**
     * 获取所有的预赔记录
     * @param p
     * @return
     */
    private List<PaymentItemDTO> getAllPrePayment(PaymentItemDTO p) {
        PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
        paymentItemDTO.setReportNo(p.getReportNo());
        paymentItemDTO.setCaseTimes(p.getCaseTimes());
        paymentItemDTO.setClaimType(CLAIM_TYPE_PRE_PAY);
        paymentItemDTO.setPolicyNo(p.getPolicyNo());
//        paymentItemDTO.setClientName(p.getClientName());
//        paymentItemDTO.setClientBankAccount(p.getClientBankAccount());
        paymentItemDTO.setPaymentType(PaymentTypeEnum.PAY.getType().equals(p.getPaymentType()) ? PaymentTypeEnum.PRE_PAY.getType() : PaymentTypeEnum.PRE_COIN_PAY.getType());
        paymentItemDTO.setCoinsuranceCompanyCode(p.getCoinsuranceCompanyCode());
        return paymentItemMapper.getPaymentItem(paymentItemDTO);
    }

    /**
     * 获取前一次该保单下该人赔付金额
     * @param p
     * @return
     */
    private List<PaymentItemDTO> getLastPayment(PaymentItemDTO p) {
        List<PaymentItemDTO> allItems = new ArrayList<>();
        Integer caseTimes = p.getCaseTimes();
        for (int i = caseTimes - 1; i > 0; i--) {
            WholeCaseBaseDTO wholeCaseBase = printService.getWholeCaseIndemnityStatus(p.getReportNo(), i);
            if (wholeCaseBase != null && (ConfigConstValues.INDEMNITYCONCLUSION_ZERO_CLOSED.equals(wholeCaseBase.getIndemnityConclusion())
                    || ConfigConstValues.INDEMNITYCONCLUSION_REFUSE.equals(wholeCaseBase.getIndemnityConclusion())
                    || ConfigConstValues.INDEMNITYCONCLUSION_CANCEL.equals(wholeCaseBase.getIndemnityConclusion()))) {
                continue;
            }
            PaymentItemDTO paymentItemDTO = new PaymentItemDTO();
            paymentItemDTO.setReportNo(p.getReportNo());
            paymentItemDTO.setCaseTimes(i);
            paymentItemDTO.setPolicyNo(p.getPolicyNo());
//            paymentItemDTO.setClientName(p.getClientName());
//            paymentItemDTO.setClientBankAccount(p.getClientBankAccount());
            paymentItemDTO.setClaimType(CLAIM_TYPE_PAY);
            paymentItemDTO.setPaymentType(p.getPaymentType());
            paymentItemDTO.setCoinsuranceCompanyCode(p.getCoinsuranceCompanyCode());
            allItems = paymentItemMapper.getPaymentItem(paymentItemDTO);
            return allItems;
        }
        return allItems;
    }

    private AdjustMaiInfo getAdjustMaiInfo(String policyNo, PaymentItemDTO itemDTO, boolean isPrePay) {
        WholeCaseBaseDTO wholeCaseBaseDTO = endCaseMapper.getWholeCaseBaseDTO(itemDTO.getReportNo(), itemDTO.getCaseTimes());
        AdjustMaiInfo adjustMaiInfo = new AdjustMaiInfo();
        adjustMaiInfo.setLossNo(itemDTO.getCaseNo());
        String lossSeqNo;
        if (isPrePay) {
            // 预赔次数 正常也不会超过十次，更不会超过99次
            Integer subTimes = itemDTO.getSubTimes();
            if (subTimes < 10) {
                lossSeqNo = "Y0" + subTimes;
            } else {
                lossSeqNo = "Y" + subTimes;
            }
        } else {
            lossSeqNo = String.valueOf(itemDTO.getCaseTimes());
        }
        adjustMaiInfo.setLossSeqNo(lossSeqNo);
        adjustMaiInfo.setClaimNo(itemDTO.getReportNo());
        adjustMaiInfo.setPolicyNo(policyNo);
        if(null != wholeCaseBaseDTO && null != wholeCaseBaseDTO.getEndCaseDate()){
            adjustMaiInfo.setUnderWriteEndDate(wholeCaseBaseDTO.getEndCaseDate());
        }else {
            adjustMaiInfo.setUnderWriteEndDate(new Date());
        }
        adjustMaiInfo.setHandlerCode(itemDTO.getCreatedBy());
        return adjustMaiInfo;
    }

    /**
     *  获取保单相关信息
     * @param customerInfo
     * @param policyNo
     * @return
     */
    private PolicyMainInfo getPolicyMainInfo(ReportCustomerInfoEntity customerInfo, String policyNo) {
        PolicyMainInfo policyMainInfo = new PolicyMainInfo();
        //根据保单号查询
        //todo tzj 送收付数据来源，直接查询承保表，global不适用 分开做逻辑，global查抄单表，承保按照原逻辑查询承保表
        //非收付必传字段，且抄单表中没存
        Map<String, String> agentByPolicyNo = new HashMap<>();
        Map<String, String> plySaleByPolicyNo = new HashMap<>();
        OcasPolicyPayDTO policyInfoForPay = new OcasPolicyPayDTO();
        if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
            //global
            plySaleByPolicyNo = ahcsPolicyInfoMapper.getPlySaleByPolicyNo(policyNo,customerInfo.getReportNo());
            policyInfoForPay = ahcsPolicyInfoMapper.getpolicyInfoForPay(customerInfo.getReportNo(),customerInfo.getClientNo());
//            当前global保单拿不到利润中心编码值，先固定传值,待global抄单接口调整
//            policyInfoForPay.setProfitCenterCode("B1");
            policyMainInfo.setPolicySource("global");
        }else{
            //oc
            agentByPolicyNo = ocasMapper.getPlyAgentByPolicyNo(policyNo);
            plySaleByPolicyNo = ocasMapper.getPlySaleByPolicyNo(policyNo);
            policyInfoForPay = ocasMapper.getPolicyInfoForPay(policyNo, customerInfo.getClientNo());
            policyMainInfo.setPolicySource("nocarcore");
        }

        policyMainInfo.setIntermediaryCode(MapUtils.getString(agentByPolicyNo,"agentCode"));
        policyMainInfo.setAgreementNo(MapUtils.getString(agentByPolicyNo,"agentAgreementNo"));
        policyMainInfo.setSolutionCode(MapUtils.getString(agentByPolicyNo,"supplementAgreementNo"));
        // 默认值 微保 ply_sale BUSINESS_SOURCE_CODE
        policyMainInfo.setChannelDetailCode(MapUtils.getString(plySaleByPolicyNo,"businessSourceCode"));
        policyMainInfo.setBusinessType("1");
        if (policyInfoForPay == null) {
            log.error(policyNo+"送收付异常，未查询到被保险人及投保人信息");
            throw new GlobalBusinessException(policyNo+"送收付异常，未查询到被保险人及投保人信息");
        }
        policyMainInfo.setIssueCompany(policyInfoForPay.getDepartmentCode());
        policyMainInfo.setInsuredCode(policyInfoForPay.getInsuredClientNo());
        policyMainInfo.setInsuredName(customerInfo.getName());
        policyMainInfo.setAppliCode(policyInfoForPay.getApplicantClientNo());
        policyMainInfo.setAppliName(policyInfoForPay.getApplicantName());
        List<CoinsureDTO> coinsureDTOList= coinsureService.getCoinsureByPolicyNo(policyNo);
        if (!CollectionUtils.isEmpty(coinsureDTOList)){
            String coinsInd = "2".equals(coinsureDTOList.get(0).getCoinsuranceType()) ? "2" : "1";
            policyMainInfo.setCoinsInd(coinsInd);
        } else {
            policyMainInfo.setCoinsInd("0");
        }
        policyMainInfo.setCompanyCode(policyInfoForPay.getDepartmentCode());
        policyMainInfo.setStartDate(policyInfoForPay.getInsuranceBeginDate());
        policyMainInfo.setEndDate(policyInfoForPay.getInsuranceEndDate());
        policyMainInfo.setProfitCenterCode(policyInfoForPay.getProfitCenterCode());
        return policyMainInfo;
    }

    private void saveCoinsureRecord(String reportNo, Integer caseTimes, Map<String, List<PaymentItemDTO>> itemGroupByPolicyNo) {
        Map<String,List<CoinsureDTO>> coinsMap = coinsureService.getCoinsureListByReportNo(reportNo);
        if (coinsMap.isEmpty()) {
            return;
        }

        List<CoinsureRecordDTO> recordList = new ArrayList<>();
        Date date = new Date();
        for (Map.Entry<String, List<PaymentItemDTO>> entry : itemGroupByPolicyNo.entrySet()) {
            String policyNo = entry.getKey();

            // 费用不分摊
            PaymentItemDTO itemDTO = entry.getValue().stream()
                    .filter(i -> (PaymentTypeEnum.PAY.getType().equals(i.getPaymentType()) || PaymentTypeEnum.PRE_PAY.getType().equals(i.getPaymentType()))).findFirst().orElse(null);
            if (Objects.isNull(itemDTO)) {
                continue;
            }

            List<CoinsureDTO> coinsureList = coinsMap.get(policyNo);
            if (CollectionUtils.isEmpty(coinsureList)) {
                continue;
            }

            // 从共不存
            CoinsureDTO coinsureDTO = coinsureList.stream().filter(i -> BaseConstant.STRING_1.equals(i.getCompanyFlag())).findFirst().orElse(null);
            if (Objects.isNull(coinsureDTO) || BaseConstant.STRING_2.equals(coinsureDTO.getCoinsuranceType())) {
                continue;
            }

            // 暂时不存非全额给付
            if (!BaseConstant.STRING_1.equals(itemDTO.getIsFullPay())) {
                continue;
            }

            CoinsureRecordDTO recordDTO = new CoinsureRecordDTO();
            recordDTO.setReportNo(reportNo);
            recordDTO.setPolicyNo(policyNo);
            List<CoinsureRecordDTO> list = coinsureRecordMapper.selectByParam(recordDTO);
            if (CollectionUtils.isEmpty(list)) {
                recordDTO.setCreatedDate(date);
                recordDTO.setCreatedBy(ConstValues.SYSTEM);
                recordDTO.setUpdatedDate(date);
                recordDTO.setUpdatedBy(ConstValues.SYSTEM);
                recordDTO.setIdAhcsCoinsureRecord(UuidUtil.getUUID());
                recordDTO.setCaseTimes(caseTimes);
                recordDTO.setIsCoinsureFee(ConstValues.NO);
                recordDTO.setIsCoinsurePay(ConstValues.YES);
                recordDTO.setArchiveTime(date);
                recordList.add(recordDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(recordList)) {
            coinsureRecordMapper.batchInsert(recordList);
        }
    }


    @Override
    public void feeInvoiceModifySendPayment(String reportNo, FeeInvoiceSendPaymentDTO feeInvoiceSendPaymentDTO) {
        log.info("feeInvoiceModifySendPayment准备调用支付接口了paySerialNo={}",feeInvoiceSendPaymentDTO.getBatchNo());
        PayInfoNotice<FeeInvoiceSendPaymentBodyDTO> payInfoNotice = new PayInfoNotice<>();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        payInfoHead.setRequestType(PayInfoHead.TYPE_Q25);
        payInfoHead.setUserCode(userCode);
        payInfoNotice.setHead(payInfoHead);
        FeeInvoiceSendPaymentBodyDTO bodyDTO = new FeeInvoiceSendPaymentBodyDTO();
        bodyDTO.setModifiedInvoice(feeInvoiceSendPaymentDTO);
        payInfoNotice.setBody(bodyDTO);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        log.info("noticePayment-feeInvoiceModifySendPayment:" + JSON.toJSONString(payInfoNotice));
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        String status = "0";
        log.info("feeInvoiceModifySendPayment准备调用支付接口开始加签paySerialNo={}",JsonUtils.toJsonString(feeInvoiceSendPaymentDTO));
        // 加签
        try {
            log.info("noticePayment-payInfoNotice-payUrl:" + payUrl);
            String result;
            if (switchMesh){
                log.info("调用支付接口用mesh方式");
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json;charset:utf-8");
                result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice),headers);
            }else {
                result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
            }

            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("noticePayment-success:" + jsonObject);
                payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                if (!payResult.isSuccess(payResult.getResponseCode())) {
                    log.info("noticePayment-fail: 收付费返回失败");
                    log.info("noticePayment-fail-errorCode: " + payResult.getErrorCode());
                    log.info("noticePayment-fail-errorMessage: " + payResult.getErrorMessage());
                    status= "1";
                }
            } else {
                log.info("noticePayment-fail: 调用接口异常");
                status= "1";
            }
        } catch (Exception e) {
            log.error("收付费接口调用异常", e);
            status= "1";
        }
        //补偿处理
        if ("1".equals(status)){
            log.error("费用修改发送收付费失败,补偿");
            asynchronousCompensation(reportNo,payInfoNotice, payResult, status,feeInvoiceSendPaymentDTO.getBatchNo());
        }
    }

    /**
     * 获取支付凭证
     * @param paymentItemDTO
     * @return 支付凭证COS地址
     */
    public String queryPaymenVoucherUrl(PaymentItemDTO paymentItemDTO) {
        String cosUrl = "";
        if (StringUtils.isNotEmpty(paymentItemDTO.getPaymentItemStatus()) && Constants.PAYMENT_ITEM_STATUS_80.equals(paymentItemDTO.getPaymentItemStatus())) {
            log.info("queryPaymenVoucherUrl从收付获取支付凭证开始：paymentItemDTO={}",JSON.toJSONString(paymentItemDTO));
            PayInfoNotice<BankElectronicFileBodyDTO> payInfoNotice = new PayInfoNotice<>();
            PayResult payResult = new PayResult();
            PayInfoHead payInfoHead = new PayInfoHead();
            payInfoHead.setPassWord(passWord);
            payInfoHead.setRequestType(PayInfoHead.TYPE_Q31);
            payInfoHead.setUserCode(userCode);
            payInfoNotice.setHead(payInfoHead);
            BankElectronicFileBodyDTO bodyDTO = new BankElectronicFileBodyDTO();
            BankElectronicFileDTO bankElectronicFileDTO = new BankElectronicFileDTO();
            bankElectronicFileDTO.setBusinessType("3");
            bankElectronicFileDTO.setBusinessNo(paymentItemDTO.getIdClmPaymentItem());
            bodyDTO.setBankElectronicFile(bankElectronicFileDTO);
            payInfoNotice.setBody(bodyDTO);
            HttpHeaders header = new HttpHeaders();
            header.add("Content-Type", "application/json;charset:utf-8");
            log.info("调用收付电子回单查询接口请求报文:" + JSON.toJSONString(payInfoNotice));
            HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
            // 加签
            try {
                String result;
                if (switchMesh){
                    log.info("调用支付接口用mesh方式");
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json;charset:utf-8");
                    result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice),headers);
                }else {
                    result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
                }
                if (StringUtils.isNotEmpty(result)) {
                    log.info("调用收付电子回单查询接口响应报文:" + result);
                    JSONObject jsonObject = JSON.parseObject(result);
                    log.info("queryPaymenVoucherUrl-success:" + jsonObject);
                    payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                    if (payResult.isSuccess(payResult.getResponseCode())) {
                        String bankElectronicFileStr = jsonObject.getJSONObject("body").getString("bankElectronicFile");
                        if (StringUtils.isNotEmpty(bankElectronicFileStr)) {
                            JSONArray jsonArray = JSON.parseArray(bankElectronicFileStr);
                            if (null != jsonArray && jsonArray.size() > 0) {
                                bankElectronicFileDTO  = jsonArray.getObject(0, BankElectronicFileDTO.class);
                                if (null != bankElectronicFileDTO && StringUtils.isNotEmpty(bankElectronicFileDTO.getCosUrl())) {
                                    cosUrl = bankElectronicFileDTO.getCosUrl();
                                } else {
                                    cosUrl= "";
                                }
                            }
                        }
                    } else {
                        log.info("queryPaymenVoucherUrl-fail: 收付费返回失败");
                        log.info("queryPaymenVoucherUrl-fail-errorCode: " + payResult.getErrorCode());
                        log.info("queryPaymenVoucherUrl-fail-errorMessage: " + payResult.getErrorMessage());
                        cosUrl= "";
                    }
                } else {
                    log.error("调用收付电子回单查询接口异常,返回信息为空！");
                    cosUrl= "";
                }
            } catch (Exception e) {
                log.error("调用收付电子回单查询接口异常", e);
                cosUrl= "";
            }
        } else {
            log.info("案件{}支付序号{}未支付成功，无电子回单,无需调用收付电子回单查询接口！" ,paymentItemDTO.getReportNo(),paymentItemDTO.getIdClmPaymentItem());
        }
        return cosUrl;
    }
    @Override
    public void noticeReplevyPayment(List<PaymentItemDTO> allItems,String reportNo, String paySerialNo) {
            if (CollectionUtils.isEmpty(allItems)) {
                throw new GlobalBusinessException("未查到待支付的支付项信息！");
            }
            ReportCustomerInfoEntity customerInfo = reportCustomerInfoService.getReportCustomerInfoByReportNo(reportNo);
            ReportInfoEntity reportInfo = reportInfoService.getReportInfo(reportNo);
            String reportSubMode = reportInfo.getReportSubMode();
            // 查询追偿信息
            Map<String, List<PaymentItemDTO>> itemGroupByPolicyNoMap = allItems.stream().collect(Collectors.groupingBy(PaymentItemDTO::getPolicyNo));
            log.info("追偿赔付信息={}", JsonUtils.toJsonString(itemGroupByPolicyNoMap));
            itemGroupByPolicyNoMap.forEach((k, v) -> {
                PaymentItemDTO itemDTO = v.get(0);
                ClaimSettlementPayment claimSettlementPayment = new ClaimSettlementPayment();
                PolicyMainInfo policyMainInfo = getPolicyMainInfo(customerInfo, k);
                claimSettlementPayment.setPolicyMainInfo(policyMainInfo);
                AdjustMaiInfo adjustMaiInfo = getReplevyAdjustMaiInfo(k, itemDTO);
                claimSettlementPayment.setAdjustMaiInfo(adjustMaiInfo);
                List<AdjustFee> adjustFees = getReplevyAdjustFees(paySerialNo, v,reportSubMode);
                claimSettlementPayment.setAdjustFee(adjustFees);
                // 接口调用需要的参数
                log.info("调用支付接口需要的参数={}",JsonUtils.toJsonString(adjustFees));
                if (!CollectionUtils.isEmpty(adjustFees)) {
                    sendPayment(paySerialNo, v, claimSettlementPayment, true);
                }
            });
            if (StringUtils.isEmptyStr(paySerialNo)) {
                allItems.forEach(item -> {
                    if (PaymentTypeEnum.FEE.getType().equals(item.getPaymentType())
                            || PaymentTypeEnum.PRE_FEE.getType().equals(item.getPaymentType())|| PaymentTypeEnum.REPLEVY_FEE.getType().equals(item.getPaymentType())) {
                        sendClaimVatInfo(item.getIdClmPaymentItem(), reportNo, item.getPolicyNo(),true);
                        item.setFinancePaymentAmount(item.getPaymentAmount());
                        paymentItemMapper.updatePaymentItem(item);
                    }
                });
            }
            // 保存共保记录
            //saveCoinsureRecord(reportNo, 1, itemGroupByPolicyNoMap);
    }
    /**
     *
     * @Description 追偿组装领款人信息
     * <AUTHOR>
     * @Date 2023/4/29 11:00
     **/
    private List<AdjustFee> getReplevyAdjustFees(String paySerialNo, List<PaymentItemDTO> paymentItems, String reportSubMode) {
        List<AdjustFee> adjustFees = new ArrayList<>();
        paymentItems.forEach(p->{
            String payeeCode = "";
            AdjustFee adjustFee = new AdjustFee();
            String idClmPaymentItem = p.getIdClmPaymentItem();
            if(!PaymentTypeEnum.REPLEVY_PAY.getType().equals(p.getPaymentType())){
                if(null != p.getCustomerNo() && StringUtils.isNotEmpty(p.getCustomerNo())){
                    payeeCode = p.getCustomerNo();
                }else{
                    InsuredPersonDTO insuredPersonDTO = insuredPersonMapper.getInsuredPersonDTO(p.getReportNo());
                    if(null != insuredPersonDTO){
                        payeeCode = insuredPersonDTO.getClientNo();
                    }
                }
            }else{
                PaymentInfoDTO paymentInfoById = paymentInfoMapper.getPaymentInfoById(idClmPaymentItem);
                if(null != paymentInfoById && StringUtils.isNotEmpty(paymentInfoById.getCustomerNo())){
                    payeeCode = paymentInfoById.getCustomerNo();
                }else {
                    InsuredPersonDTO insuredPersonDTO = insuredPersonMapper.getInsuredPersonDTO(p.getReportNo());
                    if(null != insuredPersonDTO){
                        payeeCode = insuredPersonDTO.getClientNo();
                    }
                }
            }
            if (idClmPaymentItem.equals(paySerialNo)){
                adjustFee.setTransType(AdjustFee.TRANS_TYPE_AGAIN);
            }else {
                adjustFee.setTransType(AdjustFee.TRANS_TYPE_FIRST);
            }
            adjustFee.setPayeeCode(payeeCode);
            adjustFee.setPaySerialNo(idClmPaymentItem);
            adjustFee.setPayee(p.getClientName());
            adjustFee.setCurrency("CNY");
            adjustFee.setPaidMethodCode(p.getCollectPayApproach());
            // 微保传过来：1 微信零钱 2 银行转账 3-美团支付,  送收付需要把1、2转换为：1-资金支付，2-微信支付
            String lossPayWay ;
            if ("1".equals(p.getPayType())){
                // 1 微信零钱
                lossPayWay = "2";
            }else if ("3".equals(p.getPayType())){
                // 3-美团支付
                lossPayWay = p.getPayType();
            } else {
                // 2 银行转账
                lossPayWay = "1";
            }
            adjustFee.setLossPayWay(lossPayWay);
            if(Objects.equals("1",p.getPayType()) || Objects.equals("3",p.getPayType())){
                //微信零钱或美团点评 方式 需要传openid
                adjustFee.setOpenId(p.getOpenId());
            }
            //一步结案案件送备注给收付
            List<String> reportSubModeList = Arrays.asList(reportSubModeConfig.split(","));
            adjustFee.setRemark(reportSubModeList.contains(reportSubMode) ? p.getRemark() : "");
            adjustFee.setFeeTypeCode(PaymentTypeEnum.getCode(p.getPaymentType()));
            ClmsReplevyCharge replevyChargeVo = new ClmsReplevyCharge();
            //判断是否有发票1-是，0-否，费用有发票，赔款无发票
            //追偿
            if(PaymentTypeEnum.REPLEVY_PAY.getType().equals(p.getPaymentType())){
                adjustFee.setIsExistInvoice("0");
            } else {
                adjustFee.setIsExistInvoice("1");
                //费用类型若为无发票，则送收付类型为无发票，否则送收付类型为有发票
                InvoiceInfoDTO invoiceInfo = new InvoiceInfoDTO();
                replevyChargeVo = clmsReplevyChargeMapper.getReplevyChargeByIdClmPaymentItem(idClmPaymentItem);
                if(replevyChargeVo!=null){
                    invoiceInfo = feePayMapper.getInvoiceInfoById(replevyChargeVo.getId());
                }
                if(invoiceInfo != null){
                    if("000".equals(invoiceInfo.getInvoiceType())){
                        adjustFee.setIsExistInvoice("0");
                    }else{
                        adjustFee.setIsExistInvoice("1");
                    }
                }

            }
            adjustFee.setAccountNo(p.getClientBankAccount());
            adjustFee.setPayeeBankAccountName(p.getClientName());
            String clientBankCode = p.getClientBankCode();
            String clientBankName = p.getClientBankName();
            if(null == clientBankCode){
                clientBankCode = commonParameterMapper.getBankCodeByName(clientBankName);
            }
            adjustFee.setBankPayType(p.getBankAccountAttribute());
            adjustFee.setBankCode(clientBankCode);
            adjustFee.setBankName(clientBankName);
            // 收款方/付款方银行联行号
            adjustFee.setPayeeBankCode(p.getBankDetailCode());
            adjustFee.setPayeeBankName(p.getBankDetail());

            adjustFee.setCoinsCode(p.getCoinsuranceCompanyCode());
            adjustFee.setCoinsName(p.getCoinsuranceCompanyName());

            List<AdjustmentFeeDetail> adjustmentFeeDetails = new ArrayList<>();
            List<ClmsPaymentPlan> clmsPaymentPlanList = clmsPaymentPlanService.getClmsPaymentPlanList(idClmPaymentItem);
            log.info("clmsPaymentPlanList的数据={}",JsonUtils.toJsonString(clmsPaymentPlanList));
            List<ClmsPaymentPlan> lastPaymentPlanList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(clmsPaymentPlanList)){
                BigDecimal sumPay = BigDecimal.ZERO;
                for (int i = 0; i < clmsPaymentPlanList.size(); i++) {
                    ClmsPaymentPlan clmsPaymentPlan = clmsPaymentPlanList.get(i);
                    AdjustmentFeeDetail adjustmentFeeDetail = new AdjustmentFeeDetail();//费用险种级别
                    adjustmentFeeDetail.setRiskCode(clmsPaymentPlan.getPlanCode());
                    adjustmentFeeDetail.setFeeTypeCode(PaymentTypeEnum.getCode(p.getPaymentType()));
                    BigDecimal planPayAmount = clmsPaymentPlan.getPlanPayAmount();
                    if (!CollectionUtils.isEmpty(lastPaymentPlanList)){
                        planPayAmount = planPayAmount.subtract(lastPaymentPlanList.get(i).getPlanPayAmount());
                    }
                    if (planPayAmount.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    sumPay = sumPay.add(planPayAmount);
                    adjustmentFeeDetail.setPayAmount(planPayAmount);
                    adjustmentFeeDetail.setKindCode(clmsPaymentPlan.getKindCode());
                    adjustmentFeeDetail.setProductCode(clmsPaymentPlan.getProductCode());
                    adjustmentFeeDetail.setProductLineCode(clmsPaymentPlan.getProductLineCode());
                    adjustmentFeeDetails.add(adjustmentFeeDetail);
                }
                adjustFee.setSumPaid(sumPay.doubleValue());
                p.setFinancePaymentAmount(sumPay);
            } else {
                p.setFinancePaymentAmount(BigDecimal.ZERO);
            }
            adjustFee.setAdjustmentFeeDetail(adjustmentFeeDetails);
            adjustFees.add(adjustFee);
        });
        log.info("adjustFees的值是adjustFees={}",JsonUtils.toJsonString(adjustFees));
        return adjustFees;
    }
    private AdjustMaiInfo getReplevyAdjustMaiInfo(String policyNo, PaymentItemDTO itemDTO) {
        AdjustMaiInfo adjustMaiInfo = new AdjustMaiInfo();
        adjustMaiInfo.setLossNo(itemDTO.getCaseNo());
        String reportNo = itemDTO.getReportNo();
        String lossSeqNo ="";
        Date underWriteEndDate = new Date();
        Integer subTimes = itemDTO.getSubTimes();
        if(SettleConst.PAYMENT_TYPE_REPLEVY_FEE.equals(itemDTO.getPaymentType())){
            if (subTimes < 10) {
                lossSeqNo = "F0" + subTimes;
            } else {
                lossSeqNo = "F" + subTimes;
            }
            ClmsReplevyChargeVo clmsReplevyCharge = clmsReplevyChargeMapper.selectReplevyChargeByReportNoAndSerialNo(reportNo, subTimes);
            if(clmsReplevyCharge!=null&&clmsReplevyCharge.getFinishDate()!=null){
                underWriteEndDate = clmsReplevyCharge.getFinishDate();
            }
        }else{
            if (subTimes < 10) {
                lossSeqNo = "Z0" + subTimes;
            } else {
                lossSeqNo = "Z" + subTimes;
            }
            ClmsReplevyMainVo clmsReplevyMainVo = new ClmsReplevyMainVo();
            clmsReplevyMainVo.setReportNo(reportNo);
            clmsReplevyMainVo.setReplevyTimes(subTimes);
            clmsReplevyMainVo = clmsReplevyMainMapper.selectClmsReplevyMain(clmsReplevyMainVo);
            if(clmsReplevyMainVo!=null&&clmsReplevyMainVo.getFinishDate()!=null){
                underWriteEndDate = clmsReplevyMainVo.getFinishDate();
            }
        }
        adjustMaiInfo.setUnderWriteEndDate(underWriteEndDate);
        adjustMaiInfo.setLossSeqNo(lossSeqNo);
        adjustMaiInfo.setClaimNo(itemDTO.getReportNo());
        adjustMaiInfo.setPolicyNo(policyNo);
        adjustMaiInfo.setHandlerCode(itemDTO.getCreatedBy());
        return adjustMaiInfo;
    }
    /**
     *
     * @Description 收付确认上报
     * @Date 2025/6/25 14:18
     **/
    public String sendPaymentConfirm(PayThawBody payThawBody,String reportNo,Integer caseTimes) {
        log.info("收付确认上报开始");
        PayInfoNotice<PayThawBody> payInfoNotice = new PayInfoNotice<>();
        BatchRecieveMainInfo batchRecieveMainInfo =payThawBody.getBatchRecieveMainInfo();
        PayResult payResult = new PayResult();
        PayInfoHead payInfoHead = new PayInfoHead();
        payInfoHead.setPassWord(passWord);
        payInfoHead.setRequestType(PayInfoHead.TYPE_C03);
        payInfoHead.setUserCode(userCode);
        payInfoNotice.setHead(payInfoHead);
        payInfoNotice.setBody(payThawBody);
        HttpHeaders header = new HttpHeaders();
        header.add("Content-Type", "application/json;charset:utf-8");
        log.info("sendPaymentConfirm-payInfoNotice:{}", JSON.toJSONString(payInfoNotice));
        HttpEntity<Object> httpEntity = new HttpEntity<>(payInfoNotice, header);
        String status = "0";
        String msg = "";
        // 加签
        try {
            log.info("sendPaymentConfirm-payInfoNotice-payUrl:" + payUrl);
            String result;
            if (switchMesh) {
                log.info("sendPaymentConfirm调用支付接口用mesh方式");
                Map<String, String> headers = new HashMap<>();
                headers.put("Content-Type", "application/json;charset:utf-8");
                result = MeshSendUtils.post(payUrl + "?" + printService.getSignature(), JSON.toJSONString(payInfoNotice), headers);
            } else {
                result = restTemplate.postForObject(payUrl + "?" + printService.getSignature(), httpEntity, String.class);
            }
            if (StringUtils.isNotEmpty(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                log.info("sendPaymentConfirm-success:" + jsonObject);
                payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);
                if (!payResult.isSuccess(payResult.getResponseCode())) {
                    log.info("sendPaymentConfirm-fail: 收付费返回失败");
                    log.info("sendPaymentConfirm-fail-errorCode: {}", payResult.getErrorCode());
                    log.info("sendPaymentConfirm-fail-errorMessage: {}", payResult.getErrorMessage());
                    status = "1";
                    msg = payResult.getErrorMessage();
                }
            } else {
                log.info("sendPaymentConfirm-fail: 调用接口异常");
                status = "1";
            }
        } catch (Exception e) {
            log.error("收付费接口调用异常", e);
            status= "1";
        }
        // 存送收付记录
        SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
        sendPaymentRecord.setReportNo(reportNo);
        sendPaymentRecord.setRemark(batchRecieveMainInfo.getBatchNo());
        sendPaymentRecord.setCaseTimes(caseTimes);
        sendPaymentRecord.setPaySerialNo("");
        sendPaymentRecord.setRequestType(payInfoHead.getRequestType());
        sendPaymentRecord.setRequestTypeDesc(batchRecieveMainInfo.getBatchType());
        sendPaymentRecord.setRequestParam(JSON.toJSONString(payInfoNotice));
        sendPaymentRecord.setResponseParam(payResult.getErrorMessage());
        sendPaymentRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
        sendPaymentRecordMapper.insertSelective(sendPaymentRecord);
        Long sendPaymentRecordId = sendPaymentRecord.getId();

        //补偿处理
        if ("1".equals(status)){
            reportNo = batchRecieveMainInfo.getBatchNo();
            asynchronousCompensation(reportNo,payInfoNotice, payResult, status,sendPaymentRecordId+"");
        }
        return msg;
    }
    /**
     * *报送首付冻结，解冻
     *
     * @param freezeFlag F-冻结，R-解冻
     * @return
     */
    @Override
    public PayResult sendPayThaw(String reportNo,Integer caseTimes,String bankFlowNo, BigDecimal sumAmount,String orgBusinessNo, String freezeFlag) {
        ResponseResult<Object> responseResult = new ResponseResult<Object>();
        PayResult payResult = null;
        PayInfoNotice<PayThawBody> payInfoNotice = new PayInfoNotice<PayThawBody>();
        PayInfoHead payInfoHead = new PayInfoHead();
        PayThawVo payThawVo = new PayThawVo();
        payInfoHead.setPassWord(passWord);//密码
        payInfoHead.setRequestType(PayInfoHead.TYPE_C02);//请求类型
        payInfoHead.setUserCode(userCode);//用户
        payInfoNotice.setHead(payInfoHead);//请求头
        PayThawBody payThawBody = new PayThawBody();
        payThawVo.setBusinessType("3");//业务类型，3-理赔
        payThawVo.setFreezeFlag(freezeFlag);//冻结标记，F-冻结/R-释放，必填
        payThawVo.setSystemSource("C");//系统来源，理赔：C
        //流水单号，必填,银行流水接口的业务编码
        payThawVo.setBankTransFlowNo(bankFlowNo);
        //冻结金额，CNY金额单位元，必填
        payThawVo.setAmount(sumAmount);
        //流水单号+原业务单据号唯一，必填
        payThawVo.setOriginBusinessNo(orgBusinessNo);
        payThawBody.setCashflowUseInfo(payThawVo);
        payInfoNotice.setBody(payThawBody);//放入body实体
        JSONObject jsonObject = null;
        String status = "0";
        try {
            //设置请求头格式，以及编码格式
            HttpHeaders header = new HttpHeaders();
            header.add("Content-Type", "application/json;charset:utf-8");
            log.info("调用收付冻结/解冻接口开始：", JsonUtils.toJsonString(payInfoNotice));
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json;charset:utf-8");
            String result = MeshSendUtils.post(payUrl + "?", JSON.toJSONString(payInfoNotice), headers);
            if (StringUtils.isNotEmpty(result)) {
                jsonObject = JSON.parseObject(result);
                log.info("调用收付冻结/解冻接口返回报文:" + jsonObject);
                payResult = JSON.parseObject(jsonObject.getString("head"), PayResult.class);

            }
        }catch (Exception e){
            log.error("收付费接口调用异常", e);
            status = "1";
        }
        SendPaymentRecord sendPaymentRecord = new SendPaymentRecord();
        sendPaymentRecord.setReportNo(reportNo);
        sendPaymentRecord.setRemark(freezeFlag);
        sendPaymentRecord.setCaseTimes(caseTimes);
        sendPaymentRecord.setPaySerialNo("");
        sendPaymentRecord.setRequestType(payInfoHead.getRequestType());
        sendPaymentRecord.setRequestTypeDesc("冻结/解冻");
        sendPaymentRecord.setRequestParam(JSON.toJSONString(payInfoNotice));
        sendPaymentRecord.setResponseParam(String.valueOf(jsonObject));
        sendPaymentRecord.setIsSuccess(BaseConstant.STRING_1.equals(status) ? ConstValues.NO : ConstValues.YES);
        sendPaymentRecordMapper.insertSelective(sendPaymentRecord);
        return payResult;
    }
}
