package com.paic.ncbs.claim.model.dto.pay;

import com.paic.ncbs.claim.model.dto.fee.InvoiceInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel("理赔结算DTO")
@Data
public class BatchPaymentInfo {
    @ApiModelProperty("理赔批量结算主信息")
    private BatchPaymentMainInfo batchPaymentMainInfo;

    @ApiModelProperty("理赔明细信息")
    private List<BatchPaymentDetailInfo> batchPaymentDetailInfo;

    @ApiModelProperty("发票信息")
    private List<BatchPaymentInvoice> invoiceList;
}
