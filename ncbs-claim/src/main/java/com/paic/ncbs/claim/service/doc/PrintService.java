package com.paic.ncbs.claim.service.doc;


import com.paic.ncbs.claim.dao.entity.reinsurance.ReinsBillDTO;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.doc.CreateClaimNoticeParamDTO;
import com.paic.ncbs.claim.model.dto.doc.IDGXmlPrintDTO;
import com.paic.ncbs.claim.model.dto.doc.PrintParameterDTO;
import com.paic.ncbs.claim.model.dto.print.*;
import com.paic.ncbs.claim.model.dto.reinsurance.ReinsuranceRateDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyNoNbsQueryDTO;
import com.paic.ncbs.claim.model.vo.doc.PrintDutyPayVO;
import com.paic.ncbs.claim.model.vo.doc.PrintVO;
import com.paic.ncbs.claim.model.vo.doc.TimeOutExportVo;
import com.paic.ncbs.claim.model.vo.endcase.WholeCasePageResult;
import com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface PrintService {

//    PrintVO getFormalPay(PrintDutyPayVO printDutyPayVO)throws GlobalBusinessException;
//
//    PrintParameterDTO getPrintXmlString(PrintParameterDTO printDTO)throws GlobalBusinessException;
//
//    PrintVO getZeroEndPrintInfoBusi(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException;
//
//    PrintVO getRefusePayBusi(PrintDutyPayVO printDutyPayVO)throws GlobalBusinessException;
//
//    PrintVO getFormalPayBusi(PrintDutyPayVO printDutyPayVO)throws GlobalBusinessException;
//
//    PrintVO getProtocolPayPrintInfoBusi(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException;
//
//    PrintVO getProtocolPayPrintInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException;
//
//    PrintVO getAccommodationPay(PrintDutyPayVO printDutyPayVO)throws GlobalBusinessException;
//
//    PrintVO getZeroEndPrintInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException;
//
//    PrintVO getCancelPrintInfo(PrintDutyPayVO printDutyPayVO) throws GlobalBusinessException;
//
//    PrintVO getRefusePay(PrintDutyPayVO printDutyPayVO)throws GlobalBusinessException;

    /******************newcode************************/

    WholeCasePageResult getHistoryCaseList(WholeCaseVO wholeCaseVO) throws GlobalBusinessException;

    PrintVO getFormalPayInfo(PrintDutyPayVO printDutyPayVO);

    PrintVO getProtocolPayPrintInfo(PrintDutyPayVO printDutyPayVO);

    PrintVO getZeroEndPrintInfo(PrintDutyPayVO printDutyPayVO);

    PrintVO getCancelPrintInfo(PrintDutyPayVO printDutyPayVO);

    PrintVO getRefusePay(PrintDutyPayVO printDutyPayVO);

    WholeCaseBaseDTO getWholeCaseIndemnityStatus(String reportNo, Integer caseTimes);

    PrintParameterDTO getInfoPrint(PrintVO printVO, IDGXmlPrintDTO<Object> info, String xdpName) throws Exception;

    void addPrintRecord(PrintVO printVO);

    ClmCommonPayFtlDTO getFormal(PrintVO printVO);

    ClmCollegiatePayFtlDTO copyProtocol(PrintVO printVO);

    ClmRefuseFtlDTO copyRefuse(PrintVO printVO);

    ClmZeroCancelFtlDTO copyZeroCancel(PrintVO printVO);

    ClmCancelFtlDTO copyCancel(PrintVO printVO);

    void sendPrintCore(String reportNo, Integer caseTimes);


    /**
     * 理赔通知书调用CA签章
     * @param contractParamDTO
     * @return
     */
    boolean sendClaimNoticeToCA(CreateClaimNoticeParamDTO contractParamDTO);

    /**
     * 公估委托书调用CA签章
     * @param contractParamDTO
     * @return
     */
    boolean sendClaimEntrustToCA(CreateClaimNoticeParamDTO contractParamDTO);

    void saveClaimNoticeFileId(String reportNo,Integer caseTimes,String fileId);

    void saveCommissionFileId(PrintEntrustDTO printEntrustDTO);
    String findFileId(String reportNo,Integer caseTimes);

    String findEntrustFileId(PrintEntrustDTO printEntrustDTO);

    AppraisalCommissionDTO bulidPrintInfo(PrintTemplateDTO printTemplate);

    String getSignature();

    public List<WholeCaseVO> findFileIdList(String reportNo);

    String timeOutExport(TimeOutExportVo timeOutExportVo) throws Exception ;

    void dealResponse(HttpServletResponse response, String fileName)  throws Exception ;

    /**
     * 美团上门服务-获取电子凭证
     * @param policyNoNbsQueryDTO
     * @return
     */
    String getEPolicy(PolicyNoNbsQueryDTO policyNoNbsQueryDTO);

    /**
     * 构建再保打印数据
     * @param reinsBillDTO
     * @param reportNo
     * @param caseTimes
     * @return
     */
    PrintReinsuranceDTO buildReinsurancePrintInfo(ReinsBillDTO reinsBillDTO, String reportNo, Integer caseTimes);
    boolean sendClaimReinsToCA(CreateClaimNoticeParamDTO createClaimNoticeParamDTO, String templateCode);

    void sendClaimCoinsEntrustToCA(CreateClaimNoticeParamDTO createClaimNoticeParamDTO);
}