package com.paic.ncbs.claim.dao.entity.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作记录表
 */
@Data
@TableName(value = "clm_operation_record")
public class OperationRecordEntity implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 删除标记
     */
    @TableField(value = "del_flag")
    private String delFlag;

    /**
     * 报案号
     */
    @TableField(value = "report_no")
    private String reportNo;

    /**
     * 操作类型，0-操作，1-调度，2-通知
     */
    @TableField(value = "operation_type")
    private String operationType;

    /**
     * 操作时间
     */
    @TableField(value = "operation_time")
    private Date operationTime;

    /**
     * 操作人
     */
    @TableField(value = "operation_user")
    private String operationUser;

    /**
     * 主节点
     */
    @TableField(value = "main_node")
    private String mainNode;

    /**
     * 操作节点
     */
    @TableField(value = "operation_node")
    private String operationNode;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 调度目标
     */
    @TableField(value = "target_user")
    private String targetUser;

    /**
     * 创建人员
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField(value = "sys_utime")
    private Date sysUtime;

    private static final long serialVersionUID = 1L;
}