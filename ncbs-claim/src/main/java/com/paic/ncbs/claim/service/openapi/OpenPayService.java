package com.paic.ncbs.claim.service.openapi;

import com.paic.ncbs.claim.model.dto.fee.FeeInfoDTO;
import com.paic.ncbs.claim.model.dto.openapi.FeeInvoiceBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.MergePaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentBackResultDTO;
import com.paic.ncbs.claim.model.dto.openapi.PaymentDTO;

import java.util.List;

public interface OpenPayService {

    /**
     * 接收支付结果集合
     * @param dtos 支付结果集合
     */
    void handlePayBack(List<PaymentBackResultDTO> dtos);

    /**
     * 支付信息修改
     * @param dto 支付信息
     */
    void updatePayment(PaymentDTO dto);

    /**
     * 收付费调用进项税费用发票退回
     * @param dto
     */
    void feeInvoiceBackResult(FeeInvoiceBackResultDTO dto);

    /**
     * 退运险合并支付结果
     */
    void mergePaymentBackResult(MergePaymentBackResultDTO dto);
    /**
     * 接收其他费用结算通知结果
     */
    void otherFeeSettlementResult(MergePaymentBackResultDTO dtos);
    /**
     * 退回发票id
     */
    List<FeeInfoDTO> mergeFeeInfoLists(String paySerialNo);

    void otherFeeSettlementResultCoins(MergePaymentBackResultDTO dtos);

    void coinsMergePaymentReturn(String batchNo);
}
