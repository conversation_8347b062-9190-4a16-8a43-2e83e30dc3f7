package com.paic.ncbs.claim.service.copypolicy.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.paic.ncbs.claim.common.constant.GlobalConstants;
import com.paic.ncbs.claim.common.enums.GlobalProductClassEnum;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.AmlUtils;
import com.paic.ncbs.claim.common.util.DateUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.copypolicy.ClmGlobalReturnInfo;
import com.paic.ncbs.claim.dao.entity.copypolicy.ClmSendReturnRecord;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsInsuredPresonMapper;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.copypolicy.ClmGlobalReturnInfoMapper;
import com.paic.ncbs.claim.dao.mapper.copypolicy.ClmSendReturnRecordMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimateDutyRecordMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.settle.PolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.CopyPolicyGlobalFeign;
import com.paic.ncbs.claim.model.dto.copypolicy.*;
import com.paic.ncbs.claim.model.dto.endcase.WholeCaseBaseDTO;
import com.paic.ncbs.claim.model.dto.estimate.EstimateDutyRecordDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO;
import com.paic.ncbs.claim.model.dto.riskppt.CaseRiskPropertyDTO;
import com.paic.ncbs.claim.model.dto.riskppt.RiskPropertyParamDTO;
import com.paic.ncbs.claim.model.vo.policy.PolicyRiskPropertyQueryVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.service.copypolicy.GlobalPolicyService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.policy.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
@Slf4j
public class GlobalPolicyServiceImpl implements GlobalPolicyService {
    @Autowired
    private CopyPolicyGlobalFeign copyPolicyGlobalFeign;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper;
    @Autowired
    private AmlUtils amlUtils;
    @Autowired
    private ReportInfoMapper reportInfoMapper;
    @Autowired
    private ClmSendReturnRecordMapper clmSendReturnRecordMapper;
    @Autowired
    private ClmGlobalReturnInfoMapper clmGlobalReturnInfoMapper;
    @Autowired
    private EstimateDutyRecordMapper estimateDutyRecordMapper;
    @Autowired
    private PolicyInfoMapper policyInfoMapper;
    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;
    @Autowired
    private GlobalPolicyServiceImpl globalPolicyService;
    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;
    @Autowired
    private AhcsInsuredPresonMapper ahcsInsuredPresonMapper;

    public List<OcasPolicyDTO> getpolicyList(PolicyQueryVO queryVO){
        //将入参转为TPA中台调用global保单列表查询接口的入参
        GlobalPolicyResponseDTO globalPolicyResponseDTO = transfromGlobalPolicyListParam(queryVO);
        //调用global接口
        //获取global接口回参（通用接口，与被保险人查询接口需要调用的global接口一致）
        log.info("global保单查询接口入参={}", JsonUtils.toJsonString(globalPolicyResponseDTO));
        String s = copyPolicyGlobalFeign.findPolicyListByTPA(globalPolicyResponseDTO);
        log.info("global保单查询接口出参={}",s);
        //global接口回参处理成新理赔保单列表查询回参
        globalPolicyResponseDTO = JsonUtils.toObject(s, GlobalPolicyResponseDTO.class);
        return transfromGlobalResToPolicyList(globalPolicyResponseDTO);
    }

    @Override
    public List<OcasInsuredDTO> getpolicyInsuredList(PolicyQueryVO queryVO) {
        //将入参转为TPA中台调用global保单列表查询接口的入参
        GlobalPolicyResponseDTO globalPolicyResponseDTO = transfromGlobalPolicyListParam(queryVO);
        //调用global接口
        //获取global接口回参（通用接口，与被保险人查询接口需要调用的global接口一致）
        log.info("global保单查询接口入参={}", JsonUtils.toJsonString(globalPolicyResponseDTO));
        String s = copyPolicyGlobalFeign.findPolicyListByTPA(globalPolicyResponseDTO);
        log.info("global保单查询接口出参={}",s);
        //global接口回参处理成新理赔保单列表查询回参
        globalPolicyResponseDTO = JsonUtils.toObject(s, GlobalPolicyResponseDTO.class);
        return transfromGlobalResToPolicyInsuredList(globalPolicyResponseDTO);
    }

    @Override
    public String getPolicyDetail(CopyPolicyQueryVO vo) {
        //将入参转为TPA中台调用global保单列表查询接口的入参
        GlobalPolicyResponseDTO globalPolicyResponseDTO = transfromGlobalPolicyDetailParam(vo);
        //调用global接口
        //获取global接口回参
        log.info("global抄单接口入参={}", JsonUtils.toJsonString(globalPolicyResponseDTO));
        String result = null;
        try{
            String s = copyPolicyGlobalFeign.findPolicyDetailByTPA(globalPolicyResponseDTO);
            //用保单号，被保险人名称调用保单列表查询接口
            PolicyQueryVO queryVO = new PolicyQueryVO();
            BeanUtils.copyProperties(vo,queryVO);
            queryVO.setInsuredName(vo.getName());
            GlobalPolicyResponseDTO globalPolicyResponseDTOList = transfromGlobalPolicyListParam(queryVO);
            String policylist = copyPolicyGlobalFeign.findPolicyListByTPA(globalPolicyResponseDTOList);
            log.info("global抄单接口出参={}",s);
            result = transfromGlobalResToPolicyDetail(s,policylist);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    public List<RiskPropertyParamDTO> getGlobalGroups(CopyPolicyQueryVO vo){
        List<RiskPropertyParamDTO> propertyParams = null;
        String detailStr = getPolicyDetailStr(vo);
        Map GlobalPolicyDetailMap = JSON.parseObject(detailStr,Map.class);
        Map<String,Object> responseDataMap = (Map)GlobalPolicyDetailMap.get("responseData");
        Map<String,Object> policyCopyResultInfoMap = (Map)responseDataMap.get("policyCopyResultInfo");

        String riskFlag = (String)policyCopyResultInfoMap.get("riskFlag");
        String productCode = (String)policyCopyResultInfoMap.get("productCode");

        JSONArray planInfoListJson = (JSONArray) policyCopyResultInfoMap.get("planInfoList");
        List<PlanInfoDto> planInfoDtoList = planInfoListJson.toJavaList(PlanInfoDto.class);
        if(planInfoDtoList != null && !planInfoDtoList.isEmpty()){
            propertyParams = new ArrayList<>();
            for(PlanInfoDto planInfo : planInfoDtoList) {
                RiskPropertyParamDTO riskGroup = new RiskPropertyParamDTO();
                riskGroup.setProductPackageType("");
                riskGroup.setProductCode(productCode);
                riskGroup.setProductClass(Objects.requireNonNull(GlobalProductClassEnum.getProductClassEnum(riskFlag)).getClaimProductClass());
                riskGroup.setRiskGroupNo(planInfo.getPlanCode()+"_"+planInfo.getPlanNum());
                riskGroup.setRiskGroupName(planInfo.getPlanName());
                if("GZ".equals(riskFlag)){
                    riskGroup.setRiskGroupType("996");
                }
                propertyParams.add(riskGroup);
            }
        }

        return propertyParams;
    }

    private String getPolicyDetailStr(CopyPolicyQueryVO vo) {
        //将入参转为TPA中台调用global保单列表查询接口的入参
        GlobalPolicyResponseDTO globalPolicyResponseDTO = transfromGlobalPolicyDetailParam(vo);
        //调用global接口
        //获取global接口回参
        log.info("global抄单接口入参={}", JsonUtils.toJsonString(globalPolicyResponseDTO));
        String result = null;
        try{
            result = copyPolicyGlobalFeign.findPolicyDetailByTPA(globalPolicyResponseDTO);
            log.info("global抄单接口出参={}",result);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    @Override
    public List<OcasPolicyPlanDutyDTO> getPolicyDutyDetail(CopyPolicyQueryVO vo) {
        //将入参转为TPA中台调用global保单列表查询接口的入参
        GlobalPolicyResponseDTO globalPolicyResponseDTO = transfromGlobalPolicyDetailParam(vo);
        //调用global接口
        //获取global接口回参
        log.info("global抄单接口入参={}", JsonUtils.toJsonString(globalPolicyResponseDTO));
        List<OcasPolicyPlanDutyDTO> ocasPolicyPlanDutyDTOList = new ArrayList<>();
        try{
            String s = copyPolicyGlobalFeign.findPolicyDetailByTPA(globalPolicyResponseDTO);
            //用保单号，被保险人名称调用保单列表查询接口
            PolicyQueryVO queryVO = new PolicyQueryVO();
            BeanUtils.copyProperties(vo,queryVO);
            queryVO.setInsuredName(vo.getName());
            GlobalPolicyResponseDTO globalPolicyResponseDTOList = transfromGlobalPolicyListParam(queryVO);
            String result = copyPolicyGlobalFeign.findPolicyListByTPA(globalPolicyResponseDTOList);
            log.info("global抄单接口出参={}",s);
            ocasPolicyPlanDutyDTOList = transfromGlobalResToPolicyDutyDetail(s,result);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return ocasPolicyPlanDutyDTOList;
    }

    @Override
    public ResponseResult<Object> returnReportToGlobal(String reportNo) {
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        ClmGlobalReturnInfo clmGlobalReturnInfo = new ClmGlobalReturnInfo();
        clmSendReturnRecord.setReportNo(reportNo);
        clmGlobalReturnInfo.setReportNo(reportNo);
        clmGlobalReturnInfo.setCaseTimes(1);
        //1.组装入参
        try{
        GlobalRequestDto<GlobalReturnDto<GlobalReportDto>> req = transfromGlobalReturnDtoToReport(reportNo);
        //2.接口调用
        log.info("报案回流接口入参："+JSON.toJSONString(req));
        clmSendReturnRecord.setRequestType("saveAcceptanceInfoForTPA");
        clmSendReturnRecord.setRequestParam(JSON.toJSONString(req));
        Map<String,String> productMap = ahcsPolicyInfoMapper.getPlyBaseInfo(reportNo,req.getRequestData().getAccidentReceptionIFInfo().getAgrmNo());
        String productClass = productMap.get("productClass");
        String str = "";
        if(GlobalProductClassEnum.TY.getClaimProductClass().equals(productClass)){
            //团意与其他险种的立案接口不通用，需按险种判断调用
            str = copyPolicyGlobalFeign.saveAcceptanceInfoForTPA(req);
        }else{
            str = copyPolicyGlobalFeign.saveAcceptanceInfoForKFGZ(req);
        }
        Object object = JSONObject.parse(str);
        log.info("报案接口回参："+JSON.toJSONString(object));
        clmSendReturnRecord.setResponseParam(JSON.toJSONString(object));
        Map resultMap = JSON.parseObject(str, Map.class);
        if(ObjectUtil.isNotEmpty(resultMap.get("responseData"))){
            Map responseDataMap = JSON.parseObject(JSON.toJSONString(resultMap.get("responseData")), Map.class);
            if(!"0000".equals(responseDataMap.get("resultCode"))){
                clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                clmSendReturnRecord.setRemark(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                //接口返回状态不是成功的
                throw new GlobalBusinessException(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
            }else{
                Map accidentReceptionIFInfoMap = JSON.parseObject(JSON.toJSONString(responseDataMap.get("accidentReceptionIFInfo")),Map.class);
                if(ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap) && ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap.get("rc00RcivYear"))
                && ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap.get("rc00RcivSeqNum"))){
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
                    //回流主表存数
                    clmGlobalReturnInfo.setRc00RcivYear(accidentReceptionIFInfoMap.get("rc00RcivYear").toString());
                    clmGlobalReturnInfo.setRc00RcivSeqNum(Long.valueOf(JSON.toJSONString(accidentReceptionIFInfoMap.get("rc00RcivSeqNum"))));
                    clmGlobalReturnInfo.setId(UuidUtil.getUUID());
                    clmGlobalReturnInfoMapper.insert(clmGlobalReturnInfo);
                }else{
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                    clmSendReturnRecord.setRemark("报案接口返回为空");
                }
            }
        }
        return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            log.error(e.getMessage());
            throw new GlobalBusinessException(String.valueOf(e.getMessage()));
        }finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    @Override
    public ResponseResult<Object> returnRegisterToGlobal(String reportNo) {
        ClmGlobalReturnInfo clmGlobalReturnInfo = new ClmGlobalReturnInfo();
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        clmGlobalReturnInfo.setReportNo(reportNo);
        clmSendReturnRecord.setReportNo(reportNo);
        //1.组装入参
        try{
            GlobalRequestDto<GlobalReturnDto<GlobalRegisterDto>> req = transfromGlobalReturnDtoToRegister(reportNo);
            //2.接口调用
            log.info("立案回流接口入参："+JSON.toJSONString(req));
            clmSendReturnRecord.setRequestParam(JSON.toJSONString(req));
            clmSendReturnRecord.setRequestType("saveAccidentReceptionInfoForTPA");
            String str = copyPolicyGlobalFeign.saveAccidentReceptionInfoForTPA(req);
            Object object = JSONObject.parse(str);
            clmSendReturnRecord.setResponseParam(JSON.toJSONString(object));
            log.info("立案接口回参："+JSON.toJSONString(object));
            Map resultMap = JSON.parseObject(str, Map.class);
            if(ObjectUtil.isNotEmpty(resultMap.get("responseData"))){
                Map responseDataMap = JSON.parseObject(JSON.toJSONString(resultMap.get("responseData")), Map.class);
                if(!"0000".equals(responseDataMap.get("resultCode"))){
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                    clmSendReturnRecord.setRemark(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                    //接口返回状态不是成功的
                    throw new GlobalBusinessException(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                }else{
                    Map accidentReceptionIFInfoMap = JSON.parseObject(JSON.toJSONString(responseDataMap.get("accidentReceptionIFInfo")),Map.class);
                    if(ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap)
                            && ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap.get("ac00RcivSeqNum"))
                            && ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap.get("agrmSeqNum"))
                            && ObjectUtil.isNotEmpty(accidentReceptionIFInfoMap.get("ac00RcivDate"))){
                        clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
                        //回流主表存数
                        clmGlobalReturnInfo.setAc00RcivSeqNum(Long.valueOf(JSON.toJSONString(accidentReceptionIFInfoMap.get("ac00RcivSeqNum"))));
                        clmGlobalReturnInfo.setAgrmSeqNum(Long.valueOf(JSON.toJSONString(accidentReceptionIFInfoMap.get("agrmSeqNum"))));
                        clmGlobalReturnInfo.setAc00RcivDate(accidentReceptionIFInfoMap.get("ac00RcivDate").toString());
                        clmGlobalReturnInfoMapper.updateByReportNo(clmGlobalReturnInfo);
                    }else{
                        clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                        clmSendReturnRecord.setRemark("立案接口返回为空");
                    }
                }
            }
            return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            log.error(e.getMessage());
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        }finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void createGlobalReturnRecordLog(ClmSendReturnRecord clmSendReturnRecord) {
        ClmSendReturnRecord clmSendReturnRecordOld = clmSendReturnRecordMapper.selectByReportNo(clmSendReturnRecord.getReportNo(),
                clmSendReturnRecord.getRequestType(),clmSendReturnRecord.getCaseTimes());
        if(ObjectUtil.isNotEmpty(clmSendReturnRecordOld)){
            clmSendReturnRecord.setSendTimes(clmSendReturnRecordOld.getSendTimes()+1);
            clmSendReturnRecordMapper.updateByReportNoAndType(clmSendReturnRecord);
        }else{
            clmSendReturnRecord.setSendTimes(0);
            clmSendReturnRecord.setId(UuidUtil.getUUID());
            clmSendReturnRecordMapper.insert(clmSendReturnRecord);
        }
    }

    @Override
    public ResponseResult<JSONObject> returnEstimateLoss(String reportNo,String taskType,Integer caseTimes) {
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        clmSendReturnRecord.setReportNo(reportNo);
        clmSendReturnRecord.setCaseTimes(caseTimes);
        try{
        GlobalRequestDto<GlobalReturnDto<GlobalEstimatedLossReqDTO>> globalRequestDto = new GlobalRequestDto<>();
        GlobalRequestDto<T> dto = new GlobalRequestDto<>();
        //请求头
        transformReturnRequsetHead(dto,"saveClaimReserveInfoForTPA");
        clmSendReturnRecord.setRequestType("saveClaimReserveInfoForTPA_"+taskType);
        BeanUtils.copyProperties(dto,globalRequestDto);

        //请求入参
        ClmGlobalReturnInfo clmGlobalReturnInfo = clmGlobalReturnInfoMapper.selectByReportNo(reportNo);
        GlobalEstimatedLossReqDTO globalEstimatedLossReqDTO = new GlobalEstimatedLossReqDTO();
        BeanUtils.copyProperties(clmGlobalReturnInfo,globalEstimatedLossReqDTO);

        //获取责任级估损信息
        List<ClaimReserveItem> claimReserveItemsList = getEstimateLossDutyList(reportNo,caseTimes);
        globalEstimatedLossReqDTO.setClaimReserveItems(claimReserveItemsList);

        GlobalReturnDto<GlobalEstimatedLossReqDTO> globalReturnDto = new GlobalReturnDto<>();
        globalReturnDto.setAccidentReceptionIFInfo(globalEstimatedLossReqDTO);
        globalRequestDto.setRequestData(globalReturnDto);
        //接口调用
        log.info("估损回流接口入参："+JsonUtils.toJsonString(globalRequestDto));
        clmSendReturnRecord.setRequestParam(JsonUtils.toJsonString(globalRequestDto));
        String str = copyPolicyGlobalFeign.saveClaimReserveInfoForTPA(globalRequestDto);
        log.info("估损回流接口回参："+str);
        Object globalReturnResDTO = JSONObject.parseObject(str);
        clmSendReturnRecord.setResponseParam(JSON.toJSONString(globalReturnResDTO));
        Map resultMap = JSON.parseObject(str, Map.class);
        if(ObjectUtil.isNotEmpty(resultMap.get("responseData"))){
            Map responseDataMap = JSON.parseObject(JSON.toJSONString(resultMap.get("responseData")), Map.class);
            if(!"0000".equals(responseDataMap.get("resultCode"))){
                clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                clmSendReturnRecord.setRemark(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                //接口返回状态不是成功的
                throw new GlobalBusinessException(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
            }else{
                clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
            }
        }
        return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            log.error(e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        } finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    private List<ClaimReserveItem> getEstimateLossDutyList(String reportNo, Integer caseTimes) {
        List<ClaimReserveItem> claimReserveItemsList = new ArrayList<>();
        List<EstimateDutyRecordDTO> estimateDutyRecordDTOList = estimateDutyRecordMapper.getGlobalReturnEstimateList(reportNo,caseTimes);
        if(estimateDutyRecordDTOList!=null && estimateDutyRecordDTOList.size()>0){
            for(EstimateDutyRecordDTO estimateDutyRecordDTO: estimateDutyRecordDTOList){
                //金额估损
                if(ObjectUtil.isNotEmpty(estimateDutyRecordDTO.getEstimateAmount())
                        && estimateDutyRecordDTO.getEstimateAmount().compareTo(BigDecimal.ZERO)>0){
                    ClaimReserveItem claimReserveItems = new ClaimReserveItem();
                    claimReserveItems.setMnpvCls(GlobalConstants.FEE_TYPE_AMOUNT);
                    claimReserveItems.setOsOriginalMny(estimateDutyRecordDTO.getEstimateAmount());
                    claimReserveItems.setAgrmRiskCode(estimateDutyRecordDTO.getDutyCode());
                    claimReserveItemsList.add(claimReserveItems);
                }
                //费用估损
                BigDecimal sumFee = BigDecimal.ZERO;
                sumFee = sumFee.add(estimateDutyRecordDTO.getArbitrageFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getArbitrageFee())
                        .add(estimateDutyRecordDTO.getLawsuitFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getLawsuitFee())
                        .add(estimateDutyRecordDTO.getCommonEstimateFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getCommonEstimateFee())
                        .add(estimateDutyRecordDTO.getLawyerFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getLawyerFee())
                        .add(estimateDutyRecordDTO.getExecuteFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getExecuteFee())
                        .add(estimateDutyRecordDTO.getVerifyAppraiseFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getVerifyAppraiseFee())
                        .add(estimateDutyRecordDTO.getInquireFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getInquireFee())
                        .add(estimateDutyRecordDTO.getSpecialSurveyFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getSpecialSurveyFee())
                        .add(estimateDutyRecordDTO.getOtherFee()==null?new BigDecimal(0): estimateDutyRecordDTO.getOtherFee());
                if(sumFee.compareTo(BigDecimal.ZERO)>0){
                    ClaimReserveItem claimReserveItems = new ClaimReserveItem();
                    claimReserveItems.setMnpvCls(GlobalConstants.FEE_TYPE_FEE);
                    claimReserveItems.setOsOriginalMny(sumFee);
                    claimReserveItems.setAgrmRiskCode(estimateDutyRecordDTO.getDutyCode());
                    claimReserveItemsList.add(claimReserveItems);
                }
            }
        }
        return claimReserveItemsList;
    }

    //根据保单号判断是否需要调用global估损回流接口
    @Override
    public void sendReturnEstimateLoss(String reportNo, String taskType, Integer caseTimes) {
        List<String> policyNos = policyInfoMapper.getPolicyNo(reportNo);
        if(policyNos!=null && policyNos.size()>0) {
            for (String policyNo : policyNos) {
                if (globalPolicyService.checkGlobalPolicyNo(policyNo)) {
                    this.returnEstimateLoss(reportNo, taskType, caseTimes);
                }
            }
        }
    }

    //根据保单号判断是否需要调用global理算回流接口
    @Override
    public void sendReturnSettleToGlobal(String reportNo, Integer caseTimes,String closeType,Boolean isPreFlag) {
        List<String> policyNos = policyInfoMapper.getPolicyNo(reportNo);
        if(policyNos!=null && policyNos.size()>0) {
            for (String policyNo : policyNos) {
                if (globalPolicyService.checkGlobalPolicyNo(policyNo)) {
                    //公估回流
                    this.returnAssessToGlobal(reportNo,caseTimes);
                    //理算回流
                    this.returnSettleToGlobal(reportNo, caseTimes,closeType,isPreFlag);
                }
            }
        }
    }

    //根据保单号判断是否需要调用global结案回流接口
    @Override
    public void sendReturnCloseToGlobal(String reportNo, Integer caseTimes,String closeType) {
        List<String> policyNos = policyInfoMapper.getPolicyNo(reportNo);
        if(policyNos!=null && policyNos.size()>0) {
            for (String policyNo : policyNos) {
                if (globalPolicyService.checkGlobalPolicyNo(policyNo)) {
                    WholeCaseBaseDTO wholeCaseBaseDTO = wholeCaseBaseMapper.getWholeCaseBase(reportNo,caseTimes);
                    if(ObjectUtil.isEmpty(wholeCaseBaseDTO.getIsRegister()) || "N".equals(wholeCaseBaseDTO.getIsRegister())){
                        //如果global保单结案时没有立案，则需要先调用立案回流接口，再结案，（global不支持报案注销）
                        this.returnRegisterToGlobal(reportNo);
                    }
                    //结案回流
                    this.returnCloseToGlobal(reportNo,caseTimes,closeType);
                }
            }
        }
    }

    @Override
    public Map<String,BigDecimal> getHisPayAmount(CopyPolicyQueryVO vo) {
        //将入参转为TPA中台调用global保单列表查询接口的入参
        GlobalPolicyResponseDTO globalPolicyResponseDTO = transfromGlobalPolicyDetailParam(vo);
        //调用global接口
        //获取global接口回参
        log.info("global抄单接口入参={}", JsonUtils.toJsonString(globalPolicyResponseDTO));
        Map<String,BigDecimal> result = new HashMap<>();
        try{
            String s = copyPolicyGlobalFeign.findPolicyDetailByTPA(globalPolicyResponseDTO);
            //用保单号，被保险人名称调用保单列表查询接口
            PolicyQueryVO queryVO = new PolicyQueryVO();
            BeanUtils.copyProperties(vo,queryVO);
            queryVO.setInsuredName(vo.getName());
            GlobalPolicyResponseDTO globalPolicyResponseDTOList = transfromGlobalPolicyListParam(queryVO);
            String policylist = copyPolicyGlobalFeign.findPolicyListByTPA(globalPolicyResponseDTOList);
            log.info("global抄单接口出参={}",s);
            result = getHisPaymentToGloabl(s);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return result;
    }

    private ResponseResult<Object> returnCloseToGlobal(String reportNo, Integer caseTimes, String closeType) {
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        clmSendReturnRecord.setReportNo(reportNo);
        clmSendReturnRecord.setCaseTimes(caseTimes);
        //1.组装入参
        try{
            GlobalRequestDto<GlobalCloseDto> req = transfromGlobalReturnDtoToClose(reportNo,caseTimes,closeType);
            //2.接口调用
            log.info("结案回流接口入参："+JSON.toJSONString(req));
            clmSendReturnRecord.setRequestParam(JSON.toJSONString(req));
            clmSendReturnRecord.setRequestType("openApiClaimClose");
            String str = copyPolicyGlobalFeign.openApiClaimClose(req);
            Object object = JSONObject.parse(str);
            clmSendReturnRecord.setResponseParam(JSON.toJSONString(object));
            log.info("结案接口回参："+JSON.toJSONString(object));
            Map resultMap = JSON.parseObject(str, Map.class);
            if(ObjectUtil.isNotEmpty(resultMap)){
                if(!"0000".equals(resultMap.get("resultCode"))){
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                    clmSendReturnRecord.setRemark(String.valueOf(resultMap.get("resultMsg")).replaceFirst("\n",""));
                    //接口返回状态不是成功的
                    throw new GlobalBusinessException(String.valueOf(resultMap.get("resultMsg")).replaceFirst("\n",""));
                }else{
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
                }
            }
            return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            log.error(e.getMessage());
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        }finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    private GlobalRequestDto<GlobalCloseDto> transfromGlobalReturnDtoToClose(String reportNo, Integer caseTimes, String closeType) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        GlobalRequestDto<GlobalCloseDto> globalRequestDto = new GlobalRequestDto<>();
        GlobalRequestDto<T> dto = new GlobalRequestDto<>();
        //请求头
        transformReturnRequsetHead(dto,"saveAppointmentInfoForTPA");
        BeanUtils.copyProperties(dto,globalRequestDto);

        ClmGlobalReturnInfo clmGlobalReturnInfo = clmGlobalReturnInfoMapper.selectByReportNo(reportNo);
        Map<String,String> productMap = ahcsPolicyInfoMapper.getPlyBaseInfo(reportNo,"");
        String productClass = MapUtils.getString(productMap, "productClass");
        GlobalCloseDto globalCloseDto = new GlobalCloseDto();
        globalCloseDto.setFkRcivDate(clmGlobalReturnInfo.getAc00RcivDate());
        globalCloseDto.setFkRcivSeqNum(clmGlobalReturnInfo.getAc00RcivSeqNum());
        globalCloseDto.setFkAgrmSeqNum(clmGlobalReturnInfo.getAgrmSeqNum());
        globalCloseDto.setPolicyType(GlobalProductClassEnum.getProductClassEnumTOClaim(productClass).getGlobalProductClass());
        globalCloseDto.setTerCls(closeType);
        globalRequestDto.setRequestData(globalCloseDto);
        return globalRequestDto;
    }

    private ResponseResult<Object> returnAssessToGlobal(String reportNo, Integer caseTimes) {
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        clmSendReturnRecord.setReportNo(reportNo);
        clmSendReturnRecord.setCaseTimes(caseTimes);
        //1.组装入参
        try{
            GlobalRequestDto<GlobalReturnDto<GlobalAssessDto>> req = transfromGlobalReturnDtoToAssess(reportNo,caseTimes);
            //2.接口调用
            log.info("公估回流接口入参："+JSON.toJSONString(req));
            clmSendReturnRecord.setRequestParam(JSON.toJSONString(req));
            clmSendReturnRecord.setRequestType("saveAppointmentInfoForTPA");
            String str = copyPolicyGlobalFeign.saveAppointmentInfoForTPA(req);
            Object object = JSONObject.parse(str);
            clmSendReturnRecord.setResponseParam(JSON.toJSONString(object));
            log.info("公估接口回参："+JSON.toJSONString(object));
            Map resultMap = JSON.parseObject(str, Map.class);
            if(ObjectUtil.isNotEmpty(resultMap.get("responseData"))){
                Map responseDataMap = JSON.parseObject(JSON.toJSONString(resultMap.get("responseData")), Map.class);
                if(!"0000".equals(responseDataMap.get("resultCode"))){
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                    clmSendReturnRecord.setRemark(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                    //接口返回状态不是成功的
                    throw new GlobalBusinessException(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                }else{
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
                }
            }
            return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            log.error(e.getMessage());
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        }finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    private GlobalRequestDto<GlobalReturnDto<GlobalAssessDto>> transfromGlobalReturnDtoToAssess(String reportNo, Integer caseTimes) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        GlobalRequestDto<GlobalReturnDto<GlobalAssessDto>> globalRequestDto = new GlobalRequestDto<>();
        GlobalRequestDto<T> dto = new GlobalRequestDto<>();
        //请求头
        transformReturnRequsetHead(dto,"saveAppointmentInfoForTPA");
        BeanUtils.copyProperties(dto,globalRequestDto);

        GlobalReturnDto globalReturnDto = new GlobalReturnDto<>();
        ClmGlobalReturnInfo clmGlobalReturnInfo = clmGlobalReturnInfoMapper.selectByReportNo(reportNo);
        GlobalAssessDto globalAssessDto = new GlobalAssessDto();
        BeanUtils.copyProperties(clmGlobalReturnInfo,globalAssessDto);
        GlobalAppointCompany globalAppointCompany = new GlobalAppointCompany();
        globalAppointCompany.setCmpCode("CN041");
        globalAppointCompany.setRqstCmpChrgNm("Shanxi Dazheng Loss Adjusters");
        globalAssessDto.setAppointmentCompany(globalAppointCompany);

        globalReturnDto.setAccidentReceptionIFInfo(globalAssessDto);
        globalRequestDto.setRequestData(globalReturnDto);
        return globalRequestDto;
    }

    @Override
    public ResponseResult<Object> returnSettleToGlobal(String reportNo, Integer caseTimes,String closeType,Boolean isPreFlag) {
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        clmSendReturnRecord.setReportNo(reportNo);
        clmSendReturnRecord.setCaseTimes(caseTimes);
        //1.组装入参
        try{
            GlobalRequestDto<GlobalSettleDto> req = transfromGlobalReturnDtoToSettle(reportNo,caseTimes,closeType,isPreFlag);
            //2.接口调用
            log.info("理算回流接口入参："+JSON.toJSONString(req));
            clmSendReturnRecord.setRequestParam(JSON.toJSONString(req));
            clmSendReturnRecord.setRequestType("saveDetailInfoForTPA");
            String str = copyPolicyGlobalFeign.saveDetailInfoForTPA(req);
            Object object = JSONObject.parse(str);
            clmSendReturnRecord.setResponseParam(JSON.toJSONString(object));
            log.info("理算接口回参："+JSON.toJSONString(object));
            Map resultMap = JSON.parseObject(str, Map.class);
            if(ObjectUtil.isNotEmpty(resultMap.get("responseData"))){
                Map responseDataMap = JSON.parseObject(JSON.toJSONString(resultMap.get("responseData")), Map.class);
                if(!"0000".equals(responseDataMap.get("resultCode"))){
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                    clmSendReturnRecord.setRemark(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                    //接口返回状态不是成功的
                    throw new GlobalBusinessException(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                }else{
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
                }
            }
            return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            log.error(e.getMessage());
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        }finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    @Override
    public void sendReturnRegisterAndEstimateLoss(String reportNo,Integer caseTimes) {
        List<String> policyNos = policyInfoMapper.getPolicyNo(reportNo);
        if(policyNos!=null && policyNos.size()>0){
            for(String policyNo:policyNos){
                if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                    //global保单
                    //立案回流
                    globalPolicyService.returnRegisterToGlobal(reportNo);
                    //估损回流
                    globalPolicyService.returnEstimateLoss(reportNo,"Report",caseTimes);
                }
            }
        }
    }

    @Override
    //判断是否是global保单号
    public boolean checkGlobalPolicyNo(String policyNo) {
        if(ObjectUtil.isNotEmpty(policyNo) && policyNo.length()<19){
            return true;
        }
        return false;
    }

    @Override
    public ResponseResult<JSONObject> returnRestartGlobal(String reportNo, Integer caseTimes,String policyNo) {
        ClmSendReturnRecord clmSendReturnRecord = new ClmSendReturnRecord();
        clmSendReturnRecord.setReportNo(reportNo);
        clmSendReturnRecord.setCaseTimes(caseTimes);
        try{
            GlobalRequestDto<GlobalRestartReqDto> globalRequestDto = new GlobalRequestDto<>();
            GlobalRequestDto<T> dto = new GlobalRequestDto<>();
            //请求头
            transformReturnRequsetHead(dto,"saveDetailInfoForTPA");
            clmSendReturnRecord.setRequestType("saveDetailInfoForTPA");
            BeanUtils.copyProperties(dto,globalRequestDto);

            //请求入参
            ClmGlobalReturnInfo clmGlobalReturnInfo = clmGlobalReturnInfoMapper.selectByReportNo(reportNo);
            GlobalRestartReqDto globalRestartReqDto = new GlobalRestartReqDto();
            globalRestartReqDto.setTerCls("03");
            //险种代码
            Map<String,String> productMap = ahcsPolicyInfoMapper.getPlyBaseInfo(reportNo,policyNo);
            String productClass = MapUtils.getString(productMap, "productClass");
            globalRestartReqDto.setPolicyType(productClass);
            globalRestartReqDto.setFkRcivDate(clmGlobalReturnInfo.getAc00RcivDate());
            globalRestartReqDto.setFkRcivSeqNum(clmGlobalReturnInfo.getRc00RcivSeqNum());
            globalRestartReqDto.setFkAgrmSeqNum(clmGlobalReturnInfo.getAgrmSeqNum());
            globalRequestDto.setRequestData(globalRestartReqDto);
            //接口调用
            log.info("重开接口入参："+JsonUtils.toJsonString(globalRequestDto));
            clmSendReturnRecord.setRequestParam(JsonUtils.toJsonString(globalRequestDto));
            String str = copyPolicyGlobalFeign.openApiClaimClose(globalRequestDto);
            log.info("重开接口回参："+str);
            Object globalReturnResDTO = JSONObject.parseObject(str);
            clmSendReturnRecord.setResponseParam(JSON.toJSONString(globalReturnResDTO));
            Map resultMap = JSON.parseObject(str, Map.class);
            if(ObjectUtil.isNotEmpty(resultMap.get("responseData"))){
                Map responseDataMap = JSON.parseObject(JSON.toJSONString(resultMap.get("responseData")), Map.class);
                if(!"0000".equals(responseDataMap.get("resultCode"))){
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
                    clmSendReturnRecord.setRemark(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                    //接口返回状态不是成功的
                    throw new GlobalBusinessException(String.valueOf(responseDataMap.get("resultMessage")).replaceFirst("\n",""));
                }else{
                    clmSendReturnRecord.setIsSuccess(GlobalConstants.SUCCESS);
                }
            }
            return ResponseResult.success(JSON.parseObject(str));
        } catch (Exception e) {
            //3.异常处理
            clmSendReturnRecord.setIsSuccess(GlobalConstants.FAILED);
            clmSendReturnRecord.setRemark(e.getMessage());
            log.error(e.getMessage());
            throw new GlobalBusinessException(e.getMessage());
        } finally {
            //日志记录 通过self代理调用，避免事务注解失效问题
            globalPolicyService.createGlobalReturnRecordLog(clmSendReturnRecord);
        }
    }

    @Override
    public void sendRestartToGlobal(String reportNo, Integer caseTimes) {
        List<String> policyNos = policyInfoMapper.getPolicyNo(reportNo);
        if(policyNos!=null && policyNos.size()>0){
            for(String policyNo:policyNos){
                if(globalPolicyService.checkGlobalPolicyNo(policyNo)){
                    //global保单
                    //重开
                    globalPolicyService.returnRestartGlobal(reportNo,caseTimes,policyNo);
                }
            }
        }
    }

    private GlobalRequestDto<GlobalSettleDto> transfromGlobalReturnDtoToSettle(String reportNo, Integer caseTimes,String closeType,Boolean isPreFlag) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        GlobalRequestDto<GlobalSettleDto> globalRequestDto = new GlobalRequestDto<>();
        GlobalRequestDto<T> dto = new GlobalRequestDto<>();
        //请求头
        transformReturnRequsetHead(dto,"saveDetailInfoForTPA");
        BeanUtils.copyProperties(dto,globalRequestDto);
        //请求体
        //1.查询Wholecasebase，returninfo表获取信息
        GlobalSettleDto globalSettleDto = wholeCaseBaseMapper.selectGlobalSettle(reportNo,caseTimes);
        //当前回流gobal理算接口都默认结案类型为 支付结案 1
        globalSettleDto.setTerCls(closeType);
        Map<String,String> productMap = ahcsPolicyInfoMapper.getPlyBaseInfo(reportNo,"");
        String productClass = productMap.get("productClass");
        if(GlobalProductClassEnum.GZ.getClaimProductClass().equals(productClass)){
            //责任，雇主必传
            ClaimDetailLiability claimDetailLiability = new ClaimDetailLiability();
            //损失类型（1:产品2:一般）
            claimDetailLiability.setLiabilityCls(GlobalConstants.ACC_TYPE_CODE_1);
            //具体损失类型 1 人伤 2 物损
            claimDetailLiability.setGenlLiabType(GlobalConstants.ACC_TYPE_CODE_1);
            //损失原因大类 （5.0详见损失原因大类说明）
            claimDetailLiability.setCauseLoss1(GlobalConstants.DTMNCLS_01);
            //具体国家类型（详见国家类型说明） CN000
            claimDetailLiability.setCountryCode("CN000");
            //损失描述
            claimDetailLiability.setLossDescription("");
            //出险详细地址
            claimDetailLiability.setAddressDetail("");
            //受损项目类型 （详见受损项目类型说明）
            claimDetailLiability.setItem1("");
            ClaimDetailPersonInjuryInfo claimDetailPersonInjuryInfo = new ClaimDetailPersonInjuryInfo();
            claimDetailPersonInjuryInfo = ahcsInsuredPresonMapper.getInsuredPersionByReportNo(reportNo);
//            //客户代码
//            claimDetailPersonInjuryInfo.setPtyId("");
//            //伤者姓名
//            claimDetailPersonInjuryInfo.setName("");
//            //证件类型
//            claimDetailPersonInjuryInfo.setIdType("");
//            //证件号
//            claimDetailPersonInjuryInfo.setIdNo("");
//            //性别 1男 2女
//            claimDetailPersonInjuryInfo.setSex("");
//            //出生日期
//            claimDetailPersonInjuryInfo.setBirthday("");
//            //年龄
//            claimDetailPersonInjuryInfo.setAge("");
            //是否死亡 Y 是 N 否
            claimDetailPersonInjuryInfo.setDeath(GlobalConstants.No);
            //是否伤残 Y 是 N 否
            claimDetailPersonInjuryInfo.setImpediment(GlobalConstants.No);
            //医疗费
            claimDetailPersonInjuryInfo.setMedicalFee("");
            //职业是否与投保一致 Y 是 N 否
            claimDetailPersonInjuryInfo.setSameJobYN(GlobalConstants.No);
            //是否住院 Y 是 N 否
            claimDetailPersonInjuryInfo.setHospitalYN(GlobalConstants.No);
            //医院名称
            claimDetailPersonInjuryInfo.setHospitalName("");

            claimDetailLiability.setClaimDetailPersonInjuryInfo(claimDetailPersonInjuryInfo);

            globalSettleDto.setClaimDetailLiability(claimDetailLiability);
        }else{
            //todo tzj 默认传值先
            ClaimDetailCasualty claimDetailCasualty = new ClaimDetailCasualty();
            //意健险损失类型 取值逻辑待确认 先默认 1 疾病
            claimDetailCasualty.setLossTypeForCasulaty("2");
            //具体损失类型
            claimDetailCasualty.setLossType("15");
            //被保险人是否死亡
            claimDetailCasualty.setDeathYn(GlobalConstants.No);
            //死亡时间
            claimDetailCasualty.setDeathDate("");
            //是否重大疾病
            claimDetailCasualty.setMajorDiseaseYn(GlobalConstants.No);
            //确诊时间
            claimDetailCasualty.setMajorDiseaseDate("");
            //重大疾病代码
            claimDetailCasualty.setMajorDisease("");
            //是否伤残
            claimDetailCasualty.setImpedimentYn(GlobalConstants.No);
            //残标类型
            claimDetailCasualty.setDisabilityStandardCls("");
            //伤惨日期
            claimDetailCasualty.setPermTempDate("");
            //伤残部位
            claimDetailCasualty.setBodyPart("");
            //是否为工作中
            claimDetailCasualty.setWorkingYn(GlobalConstants.No);
            //赔款币种
            claimDetailCasualty.setMedicalExpensesCurrency("CNY");
            globalSettleDto.setClaimDetailCasualty(claimDetailCasualty);
        }

        GlobalPaymentInfo globalPaymentInfo = new GlobalPaymentInfo();
        // 获取理算金额，赔付数据
        List<ClaimReserveItem> claimReserveItemsList = policyInfoMapper.getPlanDutyList(reportNo,caseTimes);
        BigDecimal sumAmount = BigDecimal.ZERO;
        BigDecimal sumFee = BigDecimal.ZERO;
        List<GlobalPaymentInsuranceInfo> globalPaymentInsuranceInfoList = new ArrayList<>();
        if(claimReserveItemsList!=null && claimReserveItemsList.size()>0){
            for(ClaimReserveItem claimReserveItem:claimReserveItemsList){
                GlobalPaymentInsuranceInfo globalPaymentInsuranceInfo = new GlobalPaymentInsuranceInfo();
                if(GlobalConstants.FEE_TYPE_AMOUNT.equals(claimReserveItem.getMnpvCls())){
                    sumAmount = sumAmount.add(claimReserveItem.getOsOriginalMny());
                    paymentInsuranceInfoCopyToClaimReserveItem(claimReserveItem,globalPaymentInsuranceInfo);
                }else{
                    sumFee = sumFee.add(claimReserveItem.getOsOriginalMny());
                    paymentInsuranceInfoCopyToClaimReserveItem(claimReserveItem,globalPaymentInsuranceInfo);
                }
                globalPaymentInsuranceInfoList.add(globalPaymentInsuranceInfo);
            }
        }
        globalPaymentInfo.setPaymentInsuranceInfoList(globalPaymentInsuranceInfoList);
        globalPaymentInfo.setConDate(sdf.format(new Date()));
        globalPaymentInfo.setSenDate(sdf.format(new Date()));
//        //只有公估费或0赔、注销时为01,其他为02
//        if(sumAmount.compareTo(BigDecimal.ZERO)>0){
//            globalPaymentInfo.setDtmnCls(GlobalConstants.DTMNCLS_02);
//        }else{
//            globalPaymentInfo.setDtmnCls(GlobalConstants.DTMNCLS_01);
//        }
        if(isPreFlag){
            //预赔
            globalPaymentInfo.setDtmnCls(GlobalConstants.DTMNCLS_01);
        }else{
            //正常赔付
            globalPaymentInfo.setDtmnCls(GlobalConstants.DTMNCLS_02);
        }
        globalPaymentInfo.setPmtDtmnSer(caseTimes);
        globalSettleDto.setPaymentInfo(globalPaymentInfo);

        GlobalRecipientInfo globalRecipientInfo = new GlobalRecipientInfo();
        //默认值 0
        globalRecipientInfo.setPmtTaxRate(BigDecimal.ZERO);
        //payAccTypeCode，领款人类型 先默认被保险人，默认值为01
        globalRecipientInfo.setPayPtyCls(GlobalConstants.PAY_ACC_TYPE_CODE_01);
        //accRelationCode，如果没有银行信息，默认值为06
        globalRecipientInfo.setRelationBenefinsured(GlobalConstants.ACC_RELATION_CODE_06);
        //共保类型  1 N/A  2 Total 3 Only Out Share 非共保默认1
        globalRecipientInfo.setCoinsTreatyType(GlobalConstants.COINS_TREATY_TYPE_1);
        globalSettleDto.setRecipientInfo(globalRecipientInfo);

        BankInfo bankInfo = new BankInfo();
        //银行信息先固定传值
        bankInfo.setBankNm("中国银行");
        bankInfo.setPayType("0001");
        bankInfo.setClientName("测试");
        bankInfo.setBankNo("****************");
        bankInfo.setBankCd("0000001");
        bankInfo.setAccountTypeCode(GlobalConstants.ACC_TYPE_CODE_0);
        globalSettleDto.setBankInfo(bankInfo);

        globalRequestDto.setRequestData(globalSettleDto);
        return globalRequestDto;
    }

    private void paymentInsuranceInfoCopyToClaimReserveItem(ClaimReserveItem claimReserveItem, GlobalPaymentInsuranceInfo globalPaymentInsuranceInfo) {
        globalPaymentInsuranceInfo.setMnpvCls(claimReserveItem.getMnpvCls());
        globalPaymentInsuranceInfo.setRiskCode(claimReserveItem.getAgrmRiskCode());
        globalPaymentInsuranceInfo.setPmtOriMny(claimReserveItem.getOsOriginalMny().setScale(2, RoundingMode.HALF_UP));
        //默认取值
        globalPaymentInsuranceInfo.setPmtDeductionAmt(BigDecimal.ZERO);
    }

    private GlobalRequestDto<GlobalReturnDto<GlobalRegisterDto>> transfromGlobalReturnDtoToRegister(String reportNo) {
        GlobalRequestDto<GlobalReturnDto<GlobalRegisterDto>> globalRequestDto = new GlobalRequestDto<>();
        GlobalRequestDto<T> dto = new GlobalRequestDto<>();
        //请求头
        transformReturnRequsetHead(dto,"saveAccidentReceptionInfoForTPA");
        BeanUtils.copyProperties(dto,globalRequestDto);
        //请求内容
        GlobalReturnDto<GlobalRegisterDto> globalReturnDto = new GlobalReturnDto<>();
        ClmGlobalReturnInfo clmGlobalReturnInfo = clmGlobalReturnInfoMapper.selectByReportNo(reportNo);
        GlobalRegisterDto globalRegisterDto = new GlobalRegisterDto();
//        globalRegisterDto.setRc00RcivYear("2025");
//        globalRegisterDto.setRc00RcivSeqNum("167");
        globalRegisterDto.setRc00RcivYear(clmGlobalReturnInfo.getRc00RcivYear());
        globalRegisterDto.setRc00RcivSeqNum(String.valueOf(clmGlobalReturnInfo.getRc00RcivSeqNum()));
        globalReturnDto.setAccidentReceptionIFInfo(globalRegisterDto);
        globalRequestDto.setRequestData(globalReturnDto);
        return globalRequestDto;
    }

    private GlobalRequestDto<GlobalReturnDto<GlobalReportDto>> transfromGlobalReturnDtoToReport(String reportNo) {
        GlobalRequestDto<GlobalReturnDto<GlobalReportDto>> globalRequestDto = new GlobalRequestDto<>();
        GlobalRequestDto<T> dto = new GlobalRequestDto<>();
        //请求头
        transformReturnRequsetHead(dto,"saveAcceptanceInfoForTPA");
        BeanUtils.copyProperties(dto,globalRequestDto);
        //请求内容
        GlobalReturnDto<GlobalReportDto> globalReturnDto = new GlobalReturnDto<>();
        GlobalReportDto globalReportDto = new GlobalReportDto();
        globalReportDto = reportInfoMapper.getGlobalReportDto(reportNo);
        globalReportDto.setClaimReportNo(reportNo);
        globalReportDto.setContactNo24("");
        globalReportDto.setRequestedMny(new BigDecimal(100));
        //报案人公司名称  报案回流不能为空，但新理赔没有这个值，默认取值
        globalReportDto.setNtfCmpName("11");
        //领款人银行信息不能为空，硬编码给虚拟信息
        globalReportDto.setBankNm("中国银行");
        globalReportDto.setBankAccountName(globalReportDto.getPibojaName());
        globalReportDto.setRelationOfBankAndPibojia(GlobalConstants.ACC_RELATION_CODE_11);
        globalReturnDto.setAccidentReceptionIFInfo(globalReportDto);
        globalRequestDto.setRequestData(globalReturnDto);
        return globalRequestDto;
    }

    private void transformReturnRequsetHead(GlobalRequestDto<?> globalRequestDto, String method) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        globalRequestDto.setRequestId(UUID.randomUUID().toString().replace("-",""));
        globalRequestDto.setRequestType("spci.claim."+ method);
        globalRequestDto.setCompanyId("********");
        globalRequestDto.setRequestTime(sdf.format(new Date()));
    }

    private List<OcasPolicyPlanDutyDTO> transfromGlobalResToPolicyDutyDetail(String s,String result) {
        List<OcasPolicyPlanDutyDTO> ocasPolicyPlanDutyDTOList = new ArrayList<>();
        //保单列表查询接口回参处理
        GlobalPolicyResponseDTO globalPolicyResponseDTO = JsonUtils.toObject(result, GlobalPolicyResponseDTO.class);
        GlobalPolicyResDataDTO globalPolicyResDataDTO = globalPolicyResponseDTO.getResponseData();
        AccidentReceptionIFInfo accidentReceptionIFInfo = globalPolicyResDataDTO.getAccidentReceptionIFInfo();
        List<RelationAgreement> relationAgreementList = accidentReceptionIFInfo.getRelationAgreementList();
        String objectCode = "";
        if(relationAgreementList!=null && relationAgreementList.size()>0) {
            for (RelationAgreement relationAgreement : relationAgreementList) {
                objectCode = relationAgreement.getObjectCode();
            }
        }

        Map GlobalPolicyDetailMap = JSON.parseObject(s,Map.class);
        Map<String,Object> responseDataMap = (Map)GlobalPolicyDetailMap.get("responseData");
        Map<String,Object> policyCopyResultInfoMap = (Map)responseDataMap.get("policyCopyResultInfo");
        JSONArray planInfoListJson = (JSONArray) policyCopyResultInfoMap.get("planInfoList");
        List<PlanInfoDto> planInfoDtoList = planInfoListJson.toJavaList(PlanInfoDto.class);
        Map<String,Object> planMap = this.getPlanMap(responseDataMap);
        //与保单列表查询接口回参中的人员下方案匹配
        String finalObjectCode = objectCode;
        if(!GlobalProductClassEnum.GZ.getProductClass().equals(String.valueOf(policyCopyResultInfoMap.get("riskFlag")))){
            planInfoDtoList = planInfoDtoList.stream().filter(plan -> finalObjectCode.equals(plan.getPlanCode()+"-"+plan.getPlanNum())).collect(Collectors.toList());
        }
        if(planInfoDtoList!=null && planInfoDtoList.size()>0){
            for (int i = 0; i < planInfoDtoList.size(); i++) {
                List<InsuranceCoverageDto> insuranceCoverageDtoList = planInfoDtoList.get(i).getInsuranceCoverageList();
                if(insuranceCoverageDtoList!=null&&insuranceCoverageDtoList.size()>0){
                    for (int j = 0; j < insuranceCoverageDtoList.size(); j++) {
                        List<String> clauseCodeList = insuranceCoverageDtoList.get(i).getClauseList();
                        if(clauseCodeList!=null && clauseCodeList.size()>0){
                            for(String clauseCode:clauseCodeList){
                                Map<String,Object> plan = (Map) planMap.get(clauseCode);
                                OcasPolicyPlanDutyDTO ocasPolicyPlanDutyDTO = new OcasPolicyPlanDutyDTO();
                                ocasPolicyPlanDutyDTO.setRiskGroupNo(planInfoDtoList.get(i).getPlanCode()+"_"+planInfoDtoList.get(i).getPlanNum());
                                ocasPolicyPlanDutyDTO.setRiskGroupName(planInfoDtoList.get(i).getPlanName());
                                ocasPolicyPlanDutyDTO.setPlanName(plan.get("clauseName").toString());
                                ocasPolicyPlanDutyDTO.setPlanCode(clauseCode);
                                ocasPolicyPlanDutyDTO.setPolicyNo(String.valueOf(policyCopyResultInfoMap.get("policyNo")));
                                ocasPolicyPlanDutyDTO.setDutyCode(insuranceCoverageDtoList.get(j).getCoverageCode());
                                ocasPolicyPlanDutyDTO.setDutyName(insuranceCoverageDtoList.get(j).getCoverageName());
                                ocasPolicyPlanDutyDTO.setDutyAmount(new BigDecimal(insuranceCoverageDtoList.get(j).getCoverageLimitAmount()));
                                ocasPolicyPlanDutyDTO.setInsuranceBeginDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("startDate")),""));
                                ocasPolicyPlanDutyDTO.setInsuranceEndDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("endDate")),""));
                                ocasPolicyPlanDutyDTO.setDutyInsuranceBeginDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("startDate")),""));
                                ocasPolicyPlanDutyDTO.setDutyInsuranceEndDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("endDate")),""));
                                ocasPolicyPlanDutyDTO.setProductName(String.valueOf(policyCopyResultInfoMap.get("productName")));
                                ocasPolicyPlanDutyDTO.setInsuredName(String.valueOf(responseDataMap.get("insuredName")));
                                ocasPolicyPlanDutyDTO.setApplicantName(String.valueOf(policyCopyResultInfoMap.get("holderName")));
                                ocasPolicyPlanDutyDTOList.add(ocasPolicyPlanDutyDTO);
                            }
                        }
                    }
                }
            }
        }

        return ocasPolicyPlanDutyDTOList;
    }

    private Map<String,Object> getPlanMap(Map<String, Object> responseDataMap) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(JsonUtils.toJsonString(responseDataMap.get("policyCopyResultInfo")));
            JsonNode dataArray = rootNode.get("insuranceClauseList");
            String keyField = "clauseCode";
            Map<String,Object> insuranceClauseMap = StreamSupport.stream(dataArray.spliterator(),false)
                    .collect(Collectors.toMap(item -> item.get(keyField).asText(),
                            item -> {
                        Map<String,Object> map = new HashMap<>();
                        item.fields().forEachRemaining(entry->{
                            map.put(entry.getKey(),entry.getValue().asText());
                        });
                        return map;
                        }
                    ));
            return insuranceClauseMap;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private String transfromGlobalResToPolicyDetail(String s,String policyList) throws ParseException {
        String result = null;
        try{
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            //保单列表查询接口回参处理
            GlobalPolicyResponseDTO globalPolicyResponseDTO = JsonUtils.toObject(policyList, GlobalPolicyResponseDTO.class);
            GlobalPolicyResDataDTO globalPolicyResDataDTO = globalPolicyResponseDTO.getResponseData();
            AccidentReceptionIFInfo accidentReceptionIFInfo = globalPolicyResDataDTO.getAccidentReceptionIFInfo();
            List<RelationAgreement> relationAgreementList = accidentReceptionIFInfo.getRelationAgreementList();
            String objectCode = "";
            if(relationAgreementList!=null && relationAgreementList.size()>0) {
                for (RelationAgreement relationAgreement : relationAgreementList) {
                    objectCode = relationAgreement.getObjectCode();
                }
            }

            Map GlobalPolicyDetailMap = JSON.parseObject(s,Map.class);
            Map<String,Object> responseDataMap = (Map)GlobalPolicyDetailMap.get("responseData");
            Map<String,Object> policyCopyResultInfoMap = (Map)responseDataMap.get("policyCopyResultInfo");
            //新理赔抄单大类
            ClaimPolicyResponseDTO claimPolicyResponseDTO = new ClaimPolicyResponseDTO();
            ClaimPolicyResDataDTO claimPolicyResDataDTO = new ClaimPolicyResDataDTO();
            ClaimContractDTO contractDTO = new ClaimContractDTO();

            BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
            baseInfoDTO.setPolicyNo(String.valueOf(responseDataMap.get("policyNo")));
            //能查到的保单默认为有效保单
            baseInfoDTO.setStatus("B5");
            baseInfoDTO.setDataSource("Global");

            //机构码值转换
            String deparetmentCode = amlUtils.branchCdToDtp(String.valueOf(responseDataMap.get("comCode")));
            baseInfoDTO.setDepartmentCode(deparetmentCode);
            //起保日期
            baseInfoDTO.setInsuranceBeginDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("startDate")),""));
            //终保日期
            baseInfoDTO.setInsuranceEndDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("endDate")),""));
            baseInfoDTO.setProductName(String.valueOf(policyCopyResultInfoMap.get("productName")));
            baseInfoDTO.setProductCode(String.valueOf(policyCopyResultInfoMap.get("productCode")));
            //todo 产品大类编码 后续需使用抄单接口的回参
            String productClass = GlobalProductClassEnum.getProductClassEnum(String.valueOf(policyCopyResultInfoMap.get("riskFlag"))).getClaimProductClass();
            baseInfoDTO.setProductClass(productClass);

            List<SelfCardActivateDTO> selfCardActivateDTOList = new ArrayList<>();
            SelfCardActivateDTO selfCardActivateDTO = new SelfCardActivateDTO();
            selfCardActivateDTO.setInsuranceBeginTime(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("startDate")),""));
            selfCardActivateDTO.setInsuranceEndTime(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("endDate")),""));
            selfCardActivateDTOList.add(selfCardActivateDTO);
            contractDTO.setselfCardActivateDTOList(selfCardActivateDTOList);

            List<RiskGroupDTO> riskGroupInfoList = new ArrayList<>();

            List<PlanDTO> planInfoList = new ArrayList<>();
            JSONArray planInfoListJson = (JSONArray) policyCopyResultInfoMap.get("planInfoList");
            List<PlanInfoDto> riskGroupList = planInfoListJson.toJavaList(PlanInfoDto.class);
            Map<String,Object> planMap = this.getPlanMap(responseDataMap);
            //与保单列表查询接口回参中的人员下方案匹配
            String finalObjectCode = objectCode;
            if(!GlobalProductClassEnum.GZ.getClaimProductClass().equals(productClass)){
                riskGroupList = riskGroupList.stream().filter(
                        plan -> finalObjectCode.equals(plan.getPlanCode() + "-" + plan.getPlanNum())).collect(Collectors.toList());
            }
            AtomicReference<Double> totalAmount = new AtomicReference<>((double) 0);

            //可赔付额度
            baseInfoDTO.setTotalInsuredAmount(totalAmount.get());

            contractDTO.setBaseInfo(baseInfoDTO);

            List<RiskPersonDTO> riskPersonInfoList = new ArrayList<>();
            //投保人信息
            List<ApplicantDTO> applicantInfoList = new ArrayList<>();
            ApplicantDTO applicantDTO = new ApplicantDTO();

            JSONArray insuredInfoJson = (JSONArray) policyCopyResultInfoMap.get("insuredInfoList");
            if(ObjectUtil.isNotEmpty(insuredInfoJson)){
                List<GlobalInsuredInfo> insuredInfoList = insuredInfoJson.toJavaList(GlobalInsuredInfo.class);
                if(GlobalProductClassEnum.GZ.getProductClass().equals(String.valueOf(policyCopyResultInfoMap.get("riskFlag")))){
                    RiskPersonDTO riskPersonDTO = new RiskPersonDTO();
                    riskPersonDTO.setName(policyCopyResultInfoMap.get("insuredName").toString());
                    riskPersonDTO.setCertificateNo(policyCopyResultInfoMap.get("idNo").toString());
                    riskPersonDTO.setCertificateType(policyCopyResultInfoMap.get("idType").toString());
                    riskPersonDTO.setClientNo(policyCopyResultInfoMap.get("insuredCode").toString());
                    riskPersonInfoList.add(riskPersonDTO);
                    //投保人名称，客户编码
                    applicantDTO.setName(policyCopyResultInfoMap.get("insuredName").toString());
                    applicantDTO.setClientNo(policyCopyResultInfoMap.get("insuredCode").toString());
                    applicantDTO.setCertificateNo(policyCopyResultInfoMap.get("holderIdNo").toString());

                    baseInfoDTO.setEndorseNo(insuredInfoList.get(0).getEventNo());
                }else{
                    if(relationAgreementList!=null && relationAgreementList.size()>0){
                        for(RelationAgreement relationAgreement:relationAgreementList){
                            if(String.valueOf(responseDataMap.get("insuredName")).equals(relationAgreement.getPibojaName())){
                                insuredInfoList = insuredInfoList.stream()
                                        .filter(insured-> insured.getInsuredCode().equals(relationAgreement.getPibojaId())
                                                && insured.getName().equals(relationAgreement.getPibojaName()))
                                        .collect(Collectors.toList());
                                RiskPersonDTO riskPersonDTO = new RiskPersonDTO();
                                riskPersonDTO.setBirthdayStr(null);
                                riskPersonDTO.setName(String.valueOf(responseDataMap.get("insuredName")));
                                riskPersonDTO.setCertificateNo(relationAgreement.getRecgNo());
                                riskPersonDTO.setCertificateType(relationAgreement.getRecgNoTypeCd());
                                riskPersonDTO.setClientNo(relationAgreement.getPibojaId());
                                riskPersonDTO.setSexCode(insuredInfoList.get(0).getSex());
                                riskPersonDTO.setBirthday(simpleDateFormat.parse(insuredInfoList.get(0).getBirthday()));
                                riskPersonDTO.setIsSociaSecurity(relationAgreement.getSocialSecurityYn());

                                //投保人名称，客户编码
                                applicantDTO.setName(relationAgreement.getPaAgrInsNm());
                                applicantDTO.setClientNo(relationAgreement.getPaAgrInsNo());
                                applicantDTO.setCertificateNo(policyCopyResultInfoMap.get("holderIdNo").toString());
                                riskPersonInfoList.add(riskPersonDTO);

                                baseInfoDTO.setEndorseNo(insuredInfoList.get(0).getEventNo());
                            }else{
                                continue;
                            }
                        }
                    }
                }
            }else {
                throw new RuntimeException("抄单被保险人清单信息为空");
            }

            applicantInfoList.add(applicantDTO);
            contractDTO.setApplicantInfoList(applicantInfoList);

            //利润中心编码值
            List<Property> propertyList = new ArrayList<>();
            if(ObjectUtil.isNotEmpty(policyCopyResultInfoMap.get("profitCenterCode"))){
                Property property = new Property();
                property.setPropertyCode("profitCenter");
                property.setPropertyValue(policyCopyResultInfoMap.get("profitCenterCode").toString());
                propertyList.add(property);
            }
            contractDTO.setPropertyList(propertyList);

            if(riskGroupList!=null && !riskGroupList.isEmpty()){
                for (int i = 0; i < riskGroupList.size(); i++) {
                    RiskGroupDTO riskGroupDTO = new RiskGroupDTO();
                    riskGroupDTO.setProductName(String.valueOf(policyCopyResultInfoMap.get("productName")));
                    riskGroupDTO.setRiskGroupName(riskGroupList.get(i).getPlanName());
                    riskGroupDTO.setRiskGroupNo(riskGroupList.get(i).getPlanCode()+"_"+riskGroupList.get(i).getPlanNum());
                    List<InsuranceCoverageDto> insuranceCoverageDtoList = riskGroupList.get(i).getInsuranceCoverageList();

                    //处理条款层级信息
                    List<PlanDTO> planList = insuranceCoverageDtoList.stream()
                        .collect(Collectors.groupingBy(coverage -> coverage.getClauseList().get(0)))  // 按 clauseList 中的第一个元素分组
                        .entrySet().stream()
                        .map(entry -> {
                            PlanDTO plan = new PlanDTO();
                            String planCode = entry.getKey();
                            Map clauses = (Map) planMap.get(planCode);
                            plan.setIsMain(clauses.get("clauseFlag").toString());
                            plan.setPlanCode(planCode);
                            plan.setPlanName(clauses.get("clauseName").toString());
                            plan.setTaxRate(Double.parseDouble(clauses.get("clauseTaxRate").toString()));
                            plan.setPolicyNo(String.valueOf(policyCopyResultInfoMap.get("policyNo")));
                            plan.setProductClass(String.valueOf(policyCopyResultInfoMap.get("riskFlag")));

                            //处理责任
                            List<InsuranceCoverageDto> globalDutyList = entry.getValue();
                            List<DutyDTO> dutyInfoList = new ArrayList<>();
                            if(globalDutyList!=null && !globalDutyList.isEmpty()){
                                for(InsuranceCoverageDto insuranceCoverageDto : globalDutyList){
                                    DutyDTO dutyDTO = new DutyDTO();

                                    dutyDTO.setDutyCode(insuranceCoverageDto.getCoverageCode());
                                    dutyDTO.setDutyName(insuranceCoverageDto.getCoverageName());
                                    dutyDTO.setInsuredAmount(Double.valueOf(insuranceCoverageDto.getCoverageLimitAmount()));
                                    totalAmount.updateAndGet(v -> v + Double.valueOf(insuranceCoverageDto.getCoverageLimitAmount()));
                                    List<RiskDutyRelationDTO> riskDutyRelationInfoList = new ArrayList<>();
                                    RiskDutyRelationDTO riskDutyRelationDTO = new RiskDutyRelationDTO();
                                    riskDutyRelationDTO.setDutyCode(insuranceCoverageDto.getCoverageCode());
                                    riskDutyRelationDTO.setInsuranceBeginDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("startDate")),""));
                                    riskDutyRelationDTO.setInsuranceEndDate(this.convertToDate(String.valueOf(policyCopyResultInfoMap.get("endDate")),""));
                                    riskDutyRelationInfoList.add(riskDutyRelationDTO);

                                    dutyDTO.setRiskDutyRelationInfoList(riskDutyRelationInfoList);
                                    dutyInfoList.add(dutyDTO);
                                }
                                plan.setDutyInfoList(dutyInfoList);
                            }
                            return plan;
                        })  // 将每个分组转换为 PlanDto 对象
                        .collect(Collectors.toList());
                    Double sumAmount =  planList.stream()
                            .flatMap(plan -> plan.getDutyInfoList().stream()) // 假设每个 PlanDTO 有一个 dutyDTOList 字段
                            .mapToDouble(DutyDTO::getInsuredAmount) // 获取每个 dutyDTO 的 InsuredAmount
                            .sum();
                    baseInfoDTO.setTotalInsuredAmount(sumAmount);
                    riskGroupDTO.setPlanInfoList(planList);
                    riskGroupDTO.setRiskPersonInfoList(riskPersonInfoList);
                    riskGroupInfoList.add(riskGroupDTO);
                }
            }

            contractDTO.setRiskGroupInfoList(riskGroupInfoList);

            ExtendDTO extendInfo = new ExtendDTO();

            contractDTO.setExtendInfo(extendInfo);

            SaleDTO saleInfo = new SaleDTO();
            //渠道编码取抄单接口提供的回参
            saleInfo.setBusinessSourceCode(ObjectUtil.isNotEmpty(policyCopyResultInfoMap.get("businessSourceCode"))
                    ? policyCopyResultInfoMap.get("businessSourceCode").toString()
                    : null);
            contractDTO.setSaleInfo(saleInfo);

            claimPolicyResDataDTO.setContractDTO(contractDTO);

            claimPolicyResDataDTO.setSaleDepartmentName(String.valueOf(policyCopyResultInfoMap.get("comName")));
            //机构代码码值转换
            claimPolicyResDataDTO.setSaleDepartmentCode(AmlUtils.branchCdToDtp(String.valueOf(policyCopyResultInfoMap.get("comCode"))));
            claimPolicyResponseDTO.setData(claimPolicyResDataDTO);
            result = JsonUtils.toJsonString(claimPolicyResponseDTO);

        } catch (Exception e) {
            log.error("global抄单参数转换失败"+e.getMessage());
        }
        return result;
    }

    private GlobalPolicyResponseDTO transfromGlobalPolicyDetailParam(CopyPolicyQueryVO vo) {
        //global保险详情查询接口入参处理
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        GlobalPolicyResponseDTO globalPolicyResponseDTO = new GlobalPolicyResponseDTO();
        globalPolicyResponseDTO.setRequestId(UUID.randomUUID().toString().replace("-",""));
        globalPolicyResponseDTO.setRequestType("spci.claim.findPolicyListByTPA");
        globalPolicyResponseDTO.setCompanyId("********");
        globalPolicyResponseDTO.setRequestTime(sdf.format(new Date()));
        GlobalPolicyResDataDTO globalPolicyResDataDTO = new GlobalPolicyResDataDTO();
        //保单号 必传
        globalPolicyResDataDTO.setPolicyNo(vo.getPolicyNo());
        //被保险人 必传
        globalPolicyResDataDTO.setInsuredName(ObjectUtil.isNotEmpty(vo.getName()) ? vo.getName() : vo.getClientName());
        //证件号
        globalPolicyResDataDTO.setIdNo(vo.getCertificateNo());
        globalPolicyResponseDTO.setRequestData(globalPolicyResDataDTO);
        return globalPolicyResponseDTO;
    }

    private List<OcasInsuredDTO> transfromGlobalResToPolicyInsuredList(GlobalPolicyResponseDTO globalPolicyResponseDTO) {
        List<OcasInsuredDTO> ocasInsuredDTOList = new ArrayList<>();
        GlobalPolicyResDataDTO globalPolicyResDataDTO = globalPolicyResponseDTO.getResponseData();
        AccidentReceptionIFInfo accidentReceptionIFInfo = globalPolicyResDataDTO.getAccidentReceptionIFInfo();
        List<RelationAgreement> relationAgreementList = accidentReceptionIFInfo.getRelationAgreementList();
        if(relationAgreementList!=null && relationAgreementList.size()>0){
            for(RelationAgreement relationAgreement:relationAgreementList) {
                OcasInsuredDTO ocasInsuredDTO = new OcasInsuredDTO();
                ocasInsuredDTO.setPolicyNo(relationAgreement.getAgrmNo());
                ocasInsuredDTO.setInsuredName(relationAgreement.getPibojaName());
                ocasInsuredDTO.setCertificateNo(relationAgreement.getRecgNo());
                //职业类别，客户类型，生日怎么对应
                //todo 证件类型 码值需要调整映射
                ocasInsuredDTO.setCertificateType(relationAgreement.getRecgNoTypeCd());
                ocasInsuredDTO.setClientNo(relationAgreement.getPibojaId());
                ocasInsuredDTOList.add(ocasInsuredDTO);
            }
        }
        //去重处理
        Map<String,List<OcasInsuredDTO>> insuredMap = ocasInsuredDTOList.stream().collect(Collectors.groupingBy(e-> (e.getInsuredName()+e.getCertificateNo()+e.getCertificateType()).toString()));
        List<OcasInsuredDTO> newInsuredList = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(insuredMap)){
            insuredMap.forEach((s, ocasInsuredDTOS) -> {
                newInsuredList.add(ocasInsuredDTOS.get(0));
            });
        }
        return newInsuredList;
    }

    private List<OcasPolicyDTO> transfromGlobalResToPolicyList(GlobalPolicyResponseDTO globalPolicyResponseDTO) {
        List<OcasPolicyDTO> ocasPolicyDTOList = new ArrayList<>();
        GlobalPolicyResDataDTO globalPolicyResDataDTO = globalPolicyResponseDTO.getResponseData();
        AccidentReceptionIFInfo accidentReceptionIFInfo = globalPolicyResDataDTO.getAccidentReceptionIFInfo();
        List<RelationAgreement> relationAgreementList = accidentReceptionIFInfo.getRelationAgreementList();
        if(relationAgreementList!=null && relationAgreementList.size()>0){
            for(RelationAgreement relationAgreement:relationAgreementList){
                OcasPolicyDTO ocasPolicyDTO = new OcasPolicyDTO();
                ocasPolicyDTO.setApplicantName(relationAgreement.getPaAgrInsNm());
                //机构码值转换
                String deparetmentCode = amlUtils.branchCdToDtp(relationAgreement.getComCode());
                ocasPolicyDTO.setDepartmentCode(deparetmentCode);
                ocasPolicyDTO.setDepartmentName(amlUtils.toManageCom(deparetmentCode));
                //保单号
                ocasPolicyDTO.setPolicyNo(relationAgreement.getAgrmNo());
                //起保日期
                Date resultDate = this.convertToDate(relationAgreement.getAgrmStartDate(),relationAgreement.getAgrmStartTime());
                ocasPolicyDTO.setInsuranceBeginDate(resultDate);
                //终保日期
                Date resultEndDate = this.convertToDate(relationAgreement.getAgrmEndDate(),relationAgreement.getAgrmEndTime());
                ocasPolicyDTO.setInsuranceEndDate(resultEndDate);
                ocasPolicyDTO.setGlobalFlag(true);
                //方案名称，渠道名称，产品名称怎么对应
                ocasPolicyDTO.setProductCode(relationAgreement.getPrdCode());
                ocasPolicyDTO.setProductName(relationAgreement.getPrdName());
                ocasPolicyDTO.setCertificateType(relationAgreement.getRecgNoTypeCd());
                ocasPolicyDTO.setCertificateNo(relationAgreement.getRecgNo());
                ocasPolicyDTO.setInsuredName(relationAgreement.getPibojaName());
                ocasPolicyDTOList.add(ocasPolicyDTO);
            }
        }
        Set<String> policySet = new HashSet<>();
        List<OcasPolicyDTO> result = ocasPolicyDTOList.stream()
                .filter(obj ->{
                    if(obj instanceof OcasPolicyDTO){
                        OcasPolicyDTO ocasPolicyDTO = (OcasPolicyDTO) obj;
                        return policySet.add(ocasPolicyDTO.getPolicyNo());
                    }
                    return false;
                }).collect(Collectors.toList());
        return result;
    }

    private Date convertToDate(String insuredStartDate, String insuredStartTime) {
        try {
//            String hourStr = insuredStartTime;
            String combined = insuredStartDate+insuredStartTime;
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = inputFormat.parse(combined);
            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateStr = outputFormat.format(date);
            return outputFormat.parse(dateStr);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private GlobalPolicyResponseDTO transfromGlobalPolicyListParam(PolicyQueryVO queryVO) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        GlobalPolicyResponseDTO globalPolicyResponseDTO = new GlobalPolicyResponseDTO();
        globalPolicyResponseDTO.setRequestId(UUID.randomUUID().toString().replace("-",""));
        globalPolicyResponseDTO.setRequestType("spci.claim.findPolicyListByTPA");
        globalPolicyResponseDTO.setCompanyId("********");
        globalPolicyResponseDTO.setRequestTime(sdf.format(new Date()));
        GlobalPolicyResDataDTO globalPolicyResDataDTO = new GlobalPolicyResDataDTO();
        AccidentReceptionIFInfo accidentReceptionIFInfo = new AccidentReceptionIFInfo();
        if(ObjectUtil.isNotEmpty(queryVO.getPolicyNo())){
            accidentReceptionIFInfo.setPlcyNo(queryVO.getPolicyNo());
        }else if(ObjectUtil.isNotEmpty(queryVO.getPolicyNoList())){
            //todo 是否需要兼容多保单情况，怎么兼容
            accidentReceptionIFInfo.setPlcyNo(queryVO.getPolicyNoList().get(0));
        }
        if(ObjectUtil.isEmpty(queryVO.getPager())){
            //当页面入参为空时默认为一页，一条
            accidentReceptionIFInfo.setCurrentPage("1");
            accidentReceptionIFInfo.setRowCntPerPage("1");
        }else{
            accidentReceptionIFInfo.setCurrentPage(String.valueOf(queryVO.getPager().getPageIndex()));
            accidentReceptionIFInfo.setRowCntPerPage(String.valueOf(queryVO.getPager().getPageRows()));
        }
        accidentReceptionIFInfo.setRecgName(queryVO.getInsuredName());
        if(ObjectUtil.isNotEmpty(queryVO.getAccidentDate())){
            accidentReceptionIFInfo.setDamageDate(simpleDateFormat.format(queryVO.getAccidentDate()));
            accidentReceptionIFInfo.setBaseDate(simpleDateFormat.format(queryVO.getAccidentDate()));
        }
        //todo 证件类型需要处理两个系统之间定义不同的映射关系
        accidentReceptionIFInfo.setRecgNoTypeCd(queryVO.getCertificateType());
        accidentReceptionIFInfo.setRecgNo(queryVO.getCertificateNo());
        globalPolicyResDataDTO.setAccidentReceptionIFInfo(accidentReceptionIFInfo);
        globalPolicyResponseDTO.setRequestData(globalPolicyResDataDTO);
        return globalPolicyResponseDTO;
    }

    public Map<String,BigDecimal> getHisPaymentToGloabl(String s){
        Map GlobalPolicyDetailMap = JSON.parseObject(s,Map.class);
        Map<String,Object> responseDataMap = (Map)GlobalPolicyDetailMap.get("responseData");
        Map<String,Object> policyCopyResultInfoMap = (Map)responseDataMap.get("policyCopyResultInfo");
        JSONArray insuredeList = (JSONArray) policyCopyResultInfoMap.get("insuredInfoList");
        Map<String,BigDecimal> paymentMap = new HashMap<>();
        if(insuredeList!=null && insuredeList.size()>0){
            for(int i = 0 ; i< insuredeList.size();i++){
                JSONObject insuede = ((JSONObject)insuredeList.get(i));
                if(insuede.get("name").equals(responseDataMap.get("insuredName"))){
                    JSONArray payMentList = (JSONArray) insuede.get("payMentList");
                    List<GlobalPayMentVo> payMentVoList = payMentList.toJavaList(GlobalPayMentVo.class);
                    Map<String,List<GlobalPayMentVo>> payMentVoMap = payMentVoList.stream().collect(Collectors.groupingBy(GlobalPayMentVo::getCoverageCode));
                    paymentMap = payMentVoMap.entrySet()
                            .stream()
                            .collect(Collectors
                                    .toMap(Map.Entry::getKey,entry-> entry.getValue()
                                            .stream()
                                            .map(GlobalPayMentVo::getPayAmount)
                                            .filter(amount->amount!=null)
                                            .collect(Collectors.reducing(BigDecimal.ZERO,BigDecimal::add))));
                }
            }
        }
        return paymentMap;
    }

    public List<PolicyRiskSubPropDTO> getRiskPropertyGlobal(PolicyRiskPropertyQueryVO riskPropQueryVO) {

        GlobalRequestDto<PolicyRiskPropertyQueryVO> globalRequestDto = new GlobalRequestDto<>();
        this.transformReturnRequsetHead(globalRequestDto,"openApiFindPolicyObjects");
        globalRequestDto.setRequestData(riskPropQueryVO);
        log.info("标的查询接口入参："+JSON.toJSONString(riskPropQueryVO));
        String res = copyPolicyGlobalFeign.openApiFindPolicyObjects(globalRequestDto);
        log.info("标的查询接口出参："+res);

        //通过global返回数据，组装标的信息
        List<PolicyRiskSubPropDTO>  riskPropertyDTOList = new ArrayList<>();
        Map resultMap = JSON.parseObject(res, Map.class);
        Map<String, Object> responseDataMap = (Map) resultMap.get("responseData");
        Map pageList = (Map) responseDataMap.get("pageList");
        List<Map> listData = (List<Map>) pageList.get("listData");
        if(listData!=null && listData.size()>0){
            for (Map map : listData) {
                PolicyRiskSubPropDTO riskPropertyDTO = new PolicyRiskSubPropDTO();
                Map insured =  (Map) map.get("insured");
                String name =  (String) ((Map) insured.get("name")).get("original");
                String idType =(String) ((Map) insured.get("idType")).get("code");
                String clientId =  (String) insured.get("id");
                String identityNo =  (String) insured.get("juminNo");
                String birthday =  (String) insured.get("birthday");
                String applyStartDate =  (String) map.get("applyStartDate");
                String applyEndDate =  (String) map.get("applyEndDate");
                applyStartDate = DateUtils.transDateString(applyStartDate,"yyyyMMdd","yyyy-MM-dd");
                applyEndDate =  DateUtils.transDateString(applyEndDate,"yyyyMMdd","yyyy-MM-dd");
                int age =  (Integer) insured.get("age");

                String socialSecurityYN =  (String) insured.get("socialSecurityYN");

                riskPropertyDTO.setPolicyNo(riskPropQueryVO.getCondition().getPolicyNo());
                riskPropertyDTO.setRiskGroupNo(riskPropQueryVO.getCondition().getObjectType() + "_" + riskPropQueryVO.getCondition().getObjectSeq());
                riskPropertyDTO.setRiskGroupType(riskPropQueryVO.getCondition().getObjectType());
                riskPropertyDTO.setRiskGroupName((String) ((Map) map.get("object")).get("originalName"));
                riskPropertyDTO.setAge(age);

                Map<String,Object> riskPropertyMap = new HashMap<>();
                riskPropertyMap.put("age",age);
                riskPropertyMap.put("birthday",birthday);
                riskPropertyMap.put("certificateType",idType);
                riskPropertyMap.put("certificateNo",identityNo);
                riskPropertyMap.put("insuranceBeginDate",applyStartDate);
                riskPropertyMap.put("name",name);
                riskPropertyMap.put("socialSecurity",socialSecurityYN);
                riskPropertyMap.put("insuranceEndDate",applyEndDate);
                riskPropertyMap.put("clientId",clientId);
                riskPropertyDTO.setRiskPropertyMap(riskPropertyMap);

                riskPropertyDTO.setRiskDetail(JSON.toJSONString(riskPropertyMap));

                riskPropertyDTOList.add(riskPropertyDTO);
            }
        }
        return riskPropertyDTOList;
    }
}
