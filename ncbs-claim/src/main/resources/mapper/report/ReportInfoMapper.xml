<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper">
    <resultMap id="BaseResultMap"
               type="com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity">
        <id column="ID_CLM_REPORT_INFO" property="idClmReportInfo"
            jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DATE" property="createdDate" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DATE" property="updatedDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_NO" property="reportNo" jdbcType="VARCHAR"/>
        <result column="REPORT_MODE" property="reportMode" jdbcType="VARCHAR"/>
        <result column="REPORT_DATE" property="reportDate" jdbcType="TIMESTAMP"/>
        <result column="DRIVER_NAME" property="driverName" jdbcType="VARCHAR"/>
        <result column="IS_CARGO_LOSS" property="isCargoLoss" jdbcType="VARCHAR"/>
        <result column="IS_CAR_LOSS" property="isCarLoss" jdbcType="VARCHAR"/>
        <result column="IS_INJURED" property="isInjured" jdbcType="VARCHAR"/>
        <result column="IS_INJURED" property="isInjured" jdbcType="VARCHAR"/>
        <result column="IS_THIRD_AGENT_REPORT" property="isThirdAgentReport"
                jdbcType="VARCHAR"/>
        <result column="IS_AGENT_CASE" property="isAgentCase" jdbcType="VARCHAR"/>
        <result column="REPORTER_NAME" property="reporterName"
                jdbcType="VARCHAR"/>
        <result column="REPORTER_CALL_NO" property="reporterCallNo"
                jdbcType="VARCHAR"/>
        <result column="REPORTER_REGISTER_TEL" property="reporterRegisterTel"
                jdbcType="VARCHAR"/>
        <result column="REPORT_REGISTER_UM" property="reportRegisterUm"
                jdbcType="VARCHAR"/>
        <result column="ACCEPT_DEPARTMENT_CODE" property="acceptDepartmentCode"
                jdbcType="VARCHAR"/>
        <result column="REPORT_OPTION" property="reportOption"
                jdbcType="VARCHAR"/>
        <result column="CANCEL_REPORT_DATE" property="cancelReportDate"
                jdbcType="TIMESTAMP"/>
        <result column="REMARK" property="remark" jdbcType="VARCHAR"/>
        <result column="MIGRATE_FROM" property="migrateFrom" jdbcType="VARCHAR"/>
        <result column="IS_WAIT_CALL" property="isWaitCall" jdbcType="VARCHAR"/>
        <result column="FLAG_SELF_SERVICE" property="flagSelfService"
                jdbcType="VARCHAR"/>
        <result column="REPAIRE_FACTORY_ID" property="repaireFactoryId"
                jdbcType="VARCHAR"/>
        <result column="REPAIR_FACTORY_NAME" property="repairFactoryName"
                jdbcType="VARCHAR"/>
        <result column="BJ_APP_CASE_NUMBER" property="bjAppCaseNumber"
                jdbcType="VARCHAR"/>
        <result column="REPORTED_LOSS_AMOUNT" property="reportedLossAmount"
                jdbcType="DECIMAL"/>
        <result column="IS_ACCEPT_DIRECT_PAY" property="isAcceptDirectPay"
                jdbcType="VARCHAR"/>
        <result column="IS_COMMUNITY_SURVEY" property="isCommunitySurvey"
                jdbcType="VARCHAR"/>
        <result column="DRIVE_SEX" property="driveSex" jdbcType="VARCHAR"/>
        <result column="DRIVE_CARD_ID" property="driveCardId" jdbcType="VARCHAR"/>
        <result column="REPORT_TYPE" property="reportType" jdbcType="VARCHAR"/>
        <result column="RISK_REMARK" property="riskRemark"/>
        <result column="RISK_FLAG" property="riskFlag"/>
        <result column="REPORT_SUB_MODE" property="reportSubMode" jdbcType="VARCHAR"/>
    </resultMap>
    
    <resultMap id="reportQueryInfo" type="com.paic.ncbs.claim.model.dto.report.ReportQueryInfoDTO">
        <result property="reportNo" column="REPORT_NO" />
        <result property="policyNo" column="POLICY_NO" />
        <result property="caseStatus" column="caseStatus" />
    </resultMap>

    <sql id="Base_Column_List">
        ID_CLM_REPORT_INFO, CREATED_BY, CREATED_DATE, UPDATED_BY,
        UPDATED_DATE, REPORT_NO,
        REPORT_MODE, REPORT_DATE, DRIVER_NAME,
        IS_CARGO_LOSS, IS_CAR_LOSS,
        IS_INJURED, REPORT_ON_PORT,
        IS_THIRD_AGENT_REPORT, IS_AGENT_CASE, REPORTER_NAME, REPORTER_CALL_NO,
        REPORTER_REGISTER_TEL,
        REPORT_REGISTER_UM, ACCEPT_DEPARTMENT_CODE,
        REPORT_OPTION, CANCEL_REPORT_DATE, REMARK,
        MIGRATE_FROM, IS_WAIT_CALL,
        FLAG_SELF_SERVICE, REPAIRE_FACTORY_ID, REPAIR_FACTORY_NAME,
        BJ_APP_CASE_NUMBER,
        REPORTED_LOSS_AMOUNT,
        IS_ACCEPT_DIRECT_PAY,
        IS_COMMUNITY_SURVEY,DRIVE_SEX,DRIVE_CARD_ID,REPORT_TYPE,
        RISK_FLAG,RISK_REMARK,REPORT_SUB_MODE
    </sql>

    <select id="getHistoryReportFilter"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="com.paic.ncbs.claim.model.dto.report.HistoryReportAgrsDTO">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        e.case_times caseTimes,
        b.name insuredName,
        b.client_type clientType,
        c.report_date reportDate,
        d.accident_date accidentDate
        from
        CLM_REPORT_INFO c,
        CLMS_POLICY_INFO m,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE e
        where
        c.report_date between
        str_to_date(#{reportDateBegin,jdbcType=VARCHAR},
        '%Y-%m-%d %H:%i:%s')
        and str_to_date(#{reportDateEnd,jdbcType=VARCHAR},
        '%Y-%m-%d %H:%i:%s')
        and m.department_code in(
        select
        dd.department_code from
        department_define dd where
        dd.upper_department_code like
        concat('%', #{departmentCode,jdbcType=VARCHAR}, '%')
        )
        <if test="reporterName != null">
            and b.name = #{reporterName,jdbcType=VARCHAR}
        </if>
        and c.report_no = m.report_no
        and c.report_no = d.report_no
        and c.report_no = e.report_no
        and c.report_no = b.report_no
        and c.migrate_from <![CDATA[!= ]]> 'o'
    </select>

    <select id="getHistoryCaseByReportNo"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        x.case_times caseTimes,
        b.name insuredName,
        b.client_type_Name clientTypeName
        from CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x
        <if test='specialCaseType == "1"'>
            ,clms_REPORT_INFO_EX e
        </if>
        where
        c.report_no=#{reportNo,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_type = '1'
        <if test='specialCaseType == "1"'>
            and e.succor_service = 'Y'
            and c.report_no = e.report_no
        </if>
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        x.case_times caseTimes,
        e.accident_name insuredName,
        null
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d ,CLM_WHOLE_CASE_BASE x , REPORT_EXC e
        <if test='specialCaseType == "1"'>
            , CLMS_REPORT_INFO_EX z
        </if>
        where c.report_no =#{reportNo,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = e.report_no
        <if test='specialCaseType == "1"'>
            and z.succor_service = 'Y'
            and c.report_no = z.report_no
        </if>
        and c.report_type = '2'
    </select>

    <select id="getHistoryCaseByReportNoFilter"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        x.case_times caseTimes,
        b.name insuredName,
        b.client_type_Name clientTypeName
        from CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x
        <if test='specialCaseType == "1"'>
            ,clms_REPORT_INFO_EX e
        </if>
        where
        c.report_no=#{reportNo,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_type = '1'
        and c.migrate_from <![CDATA[!= ]]> 'o'
        <if test='specialCaseType == "1"'>
            and e.succor_service = 'Y'
            and c.report_no = e.report_no
        </if>
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        x.case_times caseTimes,
        e.accident_name insuredName,
        null
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d ,CLM_WHOLE_CASE_BASE x , REPORT_EXC e
        <if test='specialCaseType == "1"'>
            , CLMS_REPORT_INFO_EX z
        </if>
        where c.report_no =#{reportNo,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = e.report_no
        <if test='specialCaseType == "1"'>
            and z.succor_service = 'Y'
            and c.report_no = z.report_no
        </if>
        and c.report_type = '2'
        and c.migrate_from <![CDATA[!= ]]> 'o'
    </select>

    <select id="getHistoryCaseByCertificateNo"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select
        c.report_no reportNo,
        i.report_date reportDate,
        a.accident_date accidentDate,
        w.whole_case_status caseStatus,
        w.case_times caseTimes,
        c.client_type clientType,
        c.client_type_name clientTypeName,
        c.name insuredName,
        c.client_cluster personnelAttribute
        from CLM_REPORT_INFO i,
        CLM_WHOLE_CASE_BASE w,
        CLM_REPORT_ACCIDENT a,
        CLMS_REPORT_CUSTOMER c
        where
        c.certificate_no=#{certificateNo,jdbcType=VARCHAR}
        and c.name=#{name,jdbcType=VARCHAR}
        and c.report_no=i.report_no
        and c.report_no=w.report_no
        and c.report_no=a.report_no
        and i.report_no = a.report_no
    </select>

    <select id="getHistoryCaseBetweenTime"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select
        c.report_no reportNo,
        i.report_date reportDate,
        a.accident_date accidentDate,
        w.whole_case_status caseStatus,
        w.case_times caseTimes,
        c.client_type clientType,
        c.client_type_name
        clientTypeName,
        c.CERTIFICATE_TYPE certificateType,
        c.name insuredName,
        p.product_code productCode,
        p.policy_no policyNo
        from CLM_REPORT_INFO i,
        CLM_WHOLE_CASE_BASE w,
        CLM_REPORT_ACCIDENT a,
        CLMS_REPORT_CUSTOMER c,
        CLMS_POLICY_INFO p
        where c.CERTIFICATE_NO = #{certificateNo,jdbcType=VARCHAR}
        and c.name = #{name,jdbcType=VARCHAR}
        and c.certificate_type = #{certificateType,jdbcType=VARCHAR}
        and c.report_no =i.report_no
        and c.report_no = w.report_no
        and c.report_no = a.report_no
        and c.report_no = p.report_no
        and a.accident_date between
        str_to_date(#{beginTime,jdbcType=VARCHAR},
        '%Y-%m-%d %H:%i:%s') and
        str_to_date(#{endTime,jdbcType=VARCHAR},
        '%Y-%m-%d %H:%i:%s')
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_REPORT_INFO
        where ID_CLM_REPORT_INFO =
        #{idClmReportInfo,jdbcType=VARCHAR}
    </select>

    <select id="getReportInfo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from CLM_REPORT_INFO
        where REPORT_NO = #{reportNo,jdbcType=VARCHAR}
        order by CREATED_DATE desc
        limit 1
    </select>


    <select id="selectReports" resultType="java.lang.String">
        select  distinct `REPORT_NO`  from   CLMS_REPORT_CUSTOMER  where    name  = #{name} and CERTIFICATE_NO = #{certficateNo}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from
        CLM_REPORT_INFO
        where ID_CLM_REPORT_INFO =
        #{idClmReportInfo,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity">
        insert into CLM_REPORT_INFO (ID_CLM_REPORT_INFO,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        REPORT_MODE, REPORT_DATE, DRIVER_NAME,
        IS_CARGO_LOSS, IS_CAR_LOSS,
        IS_INJURED,
        REPORT_ON_PORT, IS_THIRD_AGENT_REPORT, IS_AGENT_CASE,
        REPORTER_NAME, REPORTER_CALL_NO, REPORTER_REGISTER_TEL,
        REPORT_REGISTER_UM, ACCEPT_DEPARTMENT_CODE,
        REPORT_OPTION,
        CANCEL_REPORT_DATE, REMARK,
        MIGRATE_FROM, IS_WAIT_CALL,
        FLAG_SELF_SERVICE, REPAIRE_FACTORY_ID, REPAIR_FACTORY_NAME,
        BJ_APP_CASE_NUMBER, REPORTED_LOSS_AMOUNT, IS_ACCEPT_DIRECT_PAY,
        IS_COMMUNITY_SURVEY,DRIVE_SEX,DRIVE_CARD_ID,REPORT_TYPE,ARCHIVE_DATE,REPORT_SUB_MODE)
        values
        (#{idClmReportInfo,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR},
        #{createdDate,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR},
        #{updatedDate,jdbcType=TIMESTAMP},#{reportNo,jdbcType=VARCHAR},
        #{reportMode,jdbcType=VARCHAR}, #{reportDate,jdbcType=TIMESTAMP},
        #{driverName,jdbcType=VARCHAR},
        #{isCargoLoss,jdbcType=VARCHAR},
        #{isCarLoss,jdbcType=VARCHAR}, #{isInjured,jdbcType=VARCHAR},
        #{reportOnPort,jdbcType=VARCHAR},
        #{isThirdAgentReport,jdbcType=VARCHAR},
        #{isAgentCase,jdbcType=VARCHAR},
        #{reporterName,jdbcType=VARCHAR},
        #{reporterCallNo,jdbcType=VARCHAR},
        #{reporterRegisterTel,jdbcType=VARCHAR},
        #{reportRegisterUm,jdbcType=VARCHAR},
        #{acceptDepartmentCode,jdbcType=VARCHAR},
        #{reportOption,jdbcType=VARCHAR},
        #{cancelReportDate,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR},
        #{migrateFrom,jdbcType=VARCHAR}, #{isWaitCall,jdbcType=VARCHAR},
        #{flagSelfService,jdbcType=VARCHAR},
        #{repaireFactoryId,jdbcType=VARCHAR},
        #{repairFactoryName,jdbcType=VARCHAR},
        #{bjAppCaseNumber,jdbcType=VARCHAR},
        #{reportedLossAmount,jdbcType=DECIMAL},
        #{isAcceptDirectPay,jdbcType=VARCHAR},
        #{isCommunitySurvey,jdbcType=VARCHAR},#{driveSex,jdbcType=VARCHAR},#{driveCardId,jdbcType=VARCHAR},
        #{reportType,jdbcType=VARCHAR},now(),
        #{reportSubMode,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity">
        update CLM_REPORT_INFO
        <set>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDate != null">
                CREATED_DATE = #{createdDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDate != null">
                UPDATED_DATE = #{updatedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportNo != null">
                REPORT_NO = #{reportNo,jdbcType=VARCHAR},
            </if>
            <if test="reportMode != null">
                REPORT_MODE = #{reportMode,jdbcType=VARCHAR},
            </if>
            <if test="reportDate != null">
                REPORT_DATE = #{reportDate,jdbcType=TIMESTAMP},
            </if>
            <if test="driverName != null">
                DRIVER_NAME = #{driverName,jdbcType=VARCHAR},
            </if>
            <if test="isCargoLoss != null">
                IS_CARGO_LOSS = #{isCargoLoss,jdbcType=VARCHAR},
            </if>
            <if test="isCarLoss != null">
                IS_CAR_LOSS = #{isCarLoss,jdbcType=VARCHAR},
            </if>
            <if test="isInjured != null">
                IS_INJURED = #{isInjured,jdbcType=VARCHAR},
            </if>
            <if test="reportOnPort != null">
                REPORT_ON_PORT = #{reportOnPort,jdbcType=VARCHAR},
            </if>
            <if test="isThirdAgentReport != null">
                IS_THIRD_AGENT_REPORT =
                #{isThirdAgentReport,jdbcType=VARCHAR},
            </if>
            <if test="isAgentCase != null">
                IS_AGENT_CASE = #{isAgentCase,jdbcType=VARCHAR},
            </if>
            <if test="reporterName != null">
                REPORTER_NAME = #{reporterName,jdbcType=VARCHAR},
            </if>
            <if test="reporterCallNo != null">
                REPORTER_CALL_NO = #{reporterCallNo,jdbcType=VARCHAR},
            </if>
            <if test="reporterRegisterTel != null">
                REPORTER_REGISTER_TEL =
                #{reporterRegisterTel,jdbcType=VARCHAR},
            </if>
            <if test="reportRegisterUm != null">
                REPORT_REGISTER_UM =
                #{reportRegisterUm,jdbcType=VARCHAR},
            </if>
            <if test="acceptDepartmentCode != null">
                ACCEPT_DEPARTMENT_CODE =
                #{acceptDepartmentCode,jdbcType=VARCHAR},
            </if>
            <if test="reportOption != null">
                REPORT_OPTION = #{reportOption,jdbcType=VARCHAR},
            </if>
            <if test="cancelReportDate != null">
                CANCEL_REPORT_DATE =
                #{cancelReportDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                REMARK = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="migrateFrom != null">
                MIGRATE_FROM = #{migrateFrom,jdbcType=VARCHAR},
            </if>
            <if test="isWaitCall != null">
                IS_WAIT_CALL = #{isWaitCall,jdbcType=VARCHAR},
            </if>
            <if test="flagSelfService != null">
                FLAG_SELF_SERVICE = #{flagSelfService,jdbcType=VARCHAR},
            </if>
            <if test="repaireFactoryId != null">
                REPAIRE_FACTORY_ID =
                #{repaireFactoryId,jdbcType=VARCHAR},
            </if>
            <if test="repairFactoryName != null">
                REPAIR_FACTORY_NAME =
                #{repairFactoryName,jdbcType=VARCHAR},
            </if>
            <if test="bjAppCaseNumber != null">
                BJ_APP_CASE_NUMBER = #{bjAppCaseNumber,jdbcType=VARCHAR},
            </if>
            <if test="reportedLossAmount != null">
                REPORTED_LOSS_AMOUNT =
                #{reportedLossAmount,jdbcType=DECIMAL},
            </if>
            <if test="isAcceptDirectPay != null">
                IS_ACCEPT_DIRECT_PAY =
                #{isAcceptDirectPay,jdbcType=VARCHAR},
            </if>
            <if test="isCommunitySurvey != null">
                IS_COMMUNITY_SURVEY =
                #{isCommunitySurvey,jdbcType=VARCHAR},
            </if>
            <if test="driveSex != null">
                DRIVE_SEX = #{driveSex,jdbcType=VARCHAR},
            </if>
            <if test="isCommunitySurvey != null">
                DRIVE_CARD_ID = #{driveCardId,jdbcType=VARCHAR},
            </if>
            <if test="reportType != null">
                REPORT_TYPE = #{reportType,jdbcType=VARCHAR},
            </if>
        </set>
        where ID_CLM_REPORT_INFO = #{idClmReportInfo,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity">
        update CLM_REPORT_INFO
        set CREATED_BY =
        #{createdBy,jdbcType=VARCHAR},
        CREATED_DATE =
        #{createdDate,jdbcType=TIMESTAMP},
        UPDATED_BY =
        #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DATE =
        #{updatedDate,jdbcType=TIMESTAMP},
        REPORT_NO =
        #{reportNo,jdbcType=VARCHAR},
        REPORT_MODE =
        #{reportMode,jdbcType=VARCHAR},
        REPORT_DATE =
        #{reportDate,jdbcType=TIMESTAMP},
        DRIVER_NAME =
        #{driverName,jdbcType=VARCHAR},
        IS_CARGO_LOSS =
        #{isCargoLoss,jdbcType=VARCHAR},
        IS_CAR_LOSS =
        #{isCarLoss,jdbcType=VARCHAR},
        IS_INJURED =
        #{isInjured,jdbcType=VARCHAR},
        REPORT_ON_PORT =
        #{reportOnPort,jdbcType=VARCHAR},
        IS_THIRD_AGENT_REPORT =
        #{isThirdAgentReport,jdbcType=VARCHAR},
        IS_AGENT_CASE =
        #{isAgentCase,jdbcType=VARCHAR},
        REPORTER_NAME =
        #{reporterName,jdbcType=VARCHAR},
        REPORTER_CALL_NO =
        #{reporterCallNo,jdbcType=VARCHAR},
        REPORTER_REGISTER_TEL =
        #{reporterRegisterTel,jdbcType=VARCHAR},
        REPORT_REGISTER_UM =
        #{reportRegisterUm,jdbcType=VARCHAR},
        ACCEPT_DEPARTMENT_CODE =
        #{acceptDepartmentCode,jdbcType=VARCHAR},
        REPORT_OPTION =
        #{reportOption,jdbcType=VARCHAR},
        CANCEL_REPORT_DATE =
        #{cancelReportDate,jdbcType=TIMESTAMP},
        REMARK =
        #{remark,jdbcType=VARCHAR},
        MIGRATE_FROM =
        #{migrateFrom,jdbcType=VARCHAR},
        IS_WAIT_CALL =
        #{isWaitCall,jdbcType=VARCHAR},
        FLAG_SELF_SERVICE =
        #{flagSelfService,jdbcType=VARCHAR},
        REPAIRE_FACTORY_ID =
        #{repaireFactoryId,jdbcType=VARCHAR},
        REPAIR_FACTORY_NAME =
        #{repairFactoryName,jdbcType=VARCHAR},
        BJ_APP_CASE_NUMBER =
        #{bjAppCaseNumber,jdbcType=VARCHAR},
        REPORTED_LOSS_AMOUNT =
        #{reportedLossAmount,jdbcType=DECIMAL},
        IS_ACCEPT_DIRECT_PAY =
        #{isAcceptDirectPay,jdbcType=VARCHAR},
        IS_COMMUNITY_SURVEY =
        #{isCommunitySurvey,jdbcType=VARCHAR},
        DRIVE_SEX =
        #{driveSex,jdbcType=VARCHAR},
        DRIVE_CARD_ID =
        #{driveCardId,jdbcType=VARCHAR},
        REPORT_TYPE =
        #{reportType,jdbcType=VARCHAR}
        where ID_CLM_REPORT_INFO =
        #{idClmReportInfo,jdbcType=VARCHAR}
    </update>

    <select id="getHistoryReportByCertificateNo"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x,
        CLMS_REPORT_INFO_EX e
        where b.certificate_no = #{certificateNo,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_no = e.report_no
        and c.report_type = '1'
        <if test='specialCaseType == "1"'>
            and e.succor_service = 'Y'
        </if>
        union
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.accident_name insuredName,
        null,
        x.case_times caseTimes
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,REPORT_EXC b , CLM_WHOLE_CASE_BASE x, CLMS_REPORT_INFO_EX e
        where b.certificate_no = #{certificateNo,jdbcType=VARCHAR}
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_no = d.report_no
        and c.report_no = e.report_no
        and c.report_type = '2'
        <if test='specialCaseType == "1"'>
            and e.succor_service = 'Y'
        </if>
    </select>

    <select id="getHistoryReportByPolicyNoAndName"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLM_REPORT_INFO c,
        CLMS_POLICY_INFO m,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x,
        CLMS_REPORT_INFO_EX e
        where m.policy_no = #{policyNo,jdbcType=VARCHAR}
        <if test="name != null and name != ''">
            and b.name = #{name,jdbcType=VARCHAR}
        </if>
        <if test='specialCaseType == "1"'>
            and e.succor_service = 'Y'
        </if>
        and c.report_no = m.report_no
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_no = e.report_no
        and c.report_type = '1'
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.accident_name insuredName,
        null,
        x.case_times caseTimes
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,REPORT_EXC b ,CLM_WHOLE_CASE_BASE x, CLMS_REPORT_INFO_EX e
        where b.policy_no = #{policyNo,jdbcType=VARCHAR}
        <if test="name != null and name != ''">
            and b.accident_name = #{name,jdbcType=VARCHAR}
        </if>
        <if test='specialCaseType == "1"'>
            and e.succor_service = 'Y'
        </if>
        and c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_no = e.report_no
        and c.report_type = '2'
    </select>

    <select id="getHistoryReportByTelephoneNo"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLM_REPORT_INFO c,
        CLMS_POLICY_INFO m,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLMS_LINK_MAN l,
        CLM_WHOLE_CASE_BASE x
        where
        (l.link_man_telephone = #{telephoneNo,jdbcType=VARCHAR}
        or l.link_man_telephone = CONCAT('+86-',#{telephoneNo,jdbcType=VARCHAR})
        or c.reporter_call_no = #{telephoneNo,jdbcType=VARCHAR}
        or c.reporter_call_no = CONCAT('+86-',#{telephoneNo,jdbcType=VARCHAR}))
        and c.report_no = m.report_no
        and c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = l.report_no
        and c.report_no = x.report_no
        and c.report_type = '1'
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.accident_name insuredName,
        null,
        x.case_times caseTimes
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,REPORT_EXC b , CLMS_LINK_MAN l, CLM_WHOLE_CASE_BASE x
        where
        (l.link_man_telephone = #{telephoneNo,jdbcType=VARCHAR}
        or l.link_man_telephone = CONCAT('+86-',#{telephoneNo,jdbcType=VARCHAR})
        or c.reporter_call_no = #{telephoneNo,jdbcType=VARCHAR}
        or c.reporter_call_no = CONCAT('+86-',#{telephoneNo,jdbcType=VARCHAR}))
        and c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = l.report_no
        and c.report_no = x.report_no
        and c.report_type = '2'
    </select>

    <select id="getHistoryReportByBirthdayAndName"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x
        where b.name = #{name,jdbcType=VARCHAR}
        and b.birthday = STR_TO_DATE(#{birthday,jdbcType=VARCHAR},'%Y-%m-%d')
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_type = '1'
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.accident_name insuredName,
        null,
        x.case_times caseTimes
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,REPORT_EXC b ,CLM_WHOLE_CASE_BASE x
        where b.birthday = STR_TO_DATE(#{birthday,jdbcType=VARCHAR},'%Y-%m-%d')
        and b.accident_name= #{name,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_type = '2'
    </select>

    <select id="getHistoryReportByElectronicNoNew"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        x.case_times caseTimes,
        z.name insuredName
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,clms_POLICY_INFO b,CLM_WHOLE_CASE_BASE x,clms_REPORT_CUSTOMER
        z
        where
        c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_no = z.report_no
        and c.report_type = '1'
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        x.case_times caseTimes,
        b.accident_name insuredName
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,REPORT_EXC b,CLM_WHOLE_CASE_BASE x
        where
        c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_type = '2'
    </select>

    <select id="getHistoryReportByReportDateAndName"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="com.paic.ncbs.claim.model.dto.report.HistoryReportAgrsDTO">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x
        where b.name = #{name,jdbcType=VARCHAR}
        and
        STR_TO_DATE(#{reportDateBegin,jdbcType=VARCHAR}, '%Y-%m-%d') &lt;=
        STR_TO_DATE(DATE_FORMAT(c.report_date, '%Y-%m-%d'), '%Y-%m-%d')
        and STR_TO_DATE(#{reportDateEnd,jdbcType=VARCHAR}, '%Y-%m-%d') &gt;=
        STR_TO_DATE(DATE_FORMAT(c.report_date, '%Y-%m-%d'), '%Y-%m-%d')
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_type = '1'
        union
        select c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.accident_name insuredName,
        null,
        x.case_times caseTimes
        from CLM_REPORT_INFO c, CLM_REPORT_ACCIDENT d,REPORT_EXC b ,CLM_WHOLE_CASE_BASE x
        where
        STR_TO_DATE(#{reportDateBegin,jdbcType=VARCHAR}, '%Y-%m-%d') &lt;=
        STR_TO_DATE(DATE_FORMAT(c.report_date, '%Y-%m-%d'), '%Y-%m-%d')
        and STR_TO_DATE(#{reportDateEnd,jdbcType=VARCHAR}, '%Y-%m-%d') &gt;=
        STR_TO_DATE(DATE_FORMAT(c.report_date, '%Y-%m-%d'), '%Y-%m-%d')
        and b.accident_name= #{name,jdbcType=VARCHAR}
        and c.report_no = d.report_no
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_type = '2'
    </select>

    <select id="getHistoryReportByDateAndDepartmentCode"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="com.paic.ncbs.claim.model.dto.report.HistoryReportAgrsDTO">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        d.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT d,
        CLM_WHOLE_CASE_BASE x
        where
        (c.report_no, x.case_times) in
        <foreach collection="caseList" item="item" index="index" separator="," open="(" close=")">
            (#{item.reportNo,jdbcType=VARCHAR}, #{item.caseTimes,jdbcType=VARCHAR})
        </foreach>
        <if test="name !=null and name != ''">
            and b.name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="reportDateBegin !=null and reportDateBegin != ''">
            and STR_TO_DATE(#{reportDateBegin,jdbcType=VARCHAR}, '%Y-%m-%d') &lt;=
            STR_TO_DATE(DATE_FORMAT(c.report_date, '%Y-%m-%d'), '%Y-%m-%d')
        </if>
        <if test="reportDateEnd !=null and reportDateEnd != ''">
            and STR_TO_DATE(#{reportDateEnd,jdbcType=VARCHAR}, '%Y-%m-%d') &gt;=
            STR_TO_DATE(DATE_FORMAT(c.report_date, '%Y-%m-%d'), '%Y-%m-%d')
        </if>
        and c.report_no = d.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
    </select>

    <select id="getHistoryCaseByReportBatchNo"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="java.lang.String">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        t.accident_date accidentDate,
        b.name insuredName,
        b.client_type_Name clientTypeName,
        x.case_times caseTimes
        from CLMS_batch_report_temp t,
        CLM_REPORT_INFO c,
        CLMS_REPORT_CUSTOMER b,
        CLM_WHOLE_CASE_BASE x
        where t.report_batch_no = #{reportBatchNo,jdbcType=VARCHAR}
        and c.report_no = t.report_no
        and c.report_no = x.report_no
        and c.report_no = b.report_no
        and c.report_type = '1'
        union
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        c.report_date reportDate,
        t.accident_date accidentDate,
        b.accident_name insuredName,
        null,
        x.case_times caseTimes
        from CLMS_batch_report_temp t,
        CLM_REPORT_INFO c,
        REPORT_EXC b ,
        CLM_WHOLE_CASE_BASE x
        where t.report_batch_no = #{reportBatchNo,jdbcType=VARCHAR}
        and c.report_no = t.report_no
        and c.report_no = b.report_no
        and c.report_no = x.report_no
        and c.report_type = '2'
    </select>
    
    <select id="getHistoryCasebyClientNoBetweenDateNew" resultType="java.lang.String">
        select
        c.report_no reportNo
        from CLM_REPORT_INFO i,
        CLM_REPORT_ACCIDENT a,
        CLMS_REPORT_CUSTOMER c,
        CLMS_POLICY_INFO p
        where
        c.`CERTIFICATE_NO`= #{certificateNo, jdbcType=VARCHAR} and c.name =#{name, jdbcType=VARCHAR}
        and c.report_no =i.report_no
        and c.report_no = a.report_no
        and c.report_no = p.report_no
        and p.policy_no in (select y.policy_no from CLMS_policy_info y where y.report_no=#{reportNo, jdbcType=VARCHAR})
        <![CDATA[ AND i.report_date>= date_add(#{reportDate, jdbcType=TIMESTAMP},interval -2 day) AND i.report_date<= #{reportDate, jdbcType=TIMESTAMP}]]>
<!--        and i.report_date between-->
<!--        date_add(#{reportDate, jdbcType=DATE},interval -2 day) and #{reportDate, jdbcType=DATE}-->
        union
        select
        c.report_no reportNo
        from CLM_REPORT_INFO i,
        CLM_REPORT_ACCIDENT a,
        CLMS_REPORT_CUSTOMER c,
        CLMS_POLICY_INFO p where
        c.`CERTIFICATE_NO`= #{certificateNo, jdbcType=VARCHAR} and c.name =#{name, jdbcType=VARCHAR}
        and c.report_no =i.report_no
        and c.report_no = a.report_no
        and c.report_no = p.report_no
        and p.policy_no in (select y.policy_no from CLMS_policy_info y where y.report_no=#{reportNo, jdbcType=VARCHAR})
        <![CDATA[ AND a.accident_date>= date_add(#{accidentDate, jdbcType=TIMESTAMP},interval -1 day) AND a.accident_date<= #{accidentDate, jdbcType=TIMESTAMP}]]>
<!--        and a.accident_date between-->
<!--        date_add(#{accidentDate, jdbcType=DATE},interval -1 day) and-->
<!--        date_add(#{accidentDate, jdbcType=DATE},interval 1 day)-->
    </select>

    <select id="getHistoryReport"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="com.paic.ncbs.claim.model.dto.report.HistoryReportAgrsDTO">
        select distinct
        c.report_type reportType,
        c.report_no reportNo,
        e.case_times caseTimes,
        b.name insuredName,
        b.client_type clientType,
        c.report_date reportDate,
        d.accident_date accidentDate
        from
        CLM_REPORT_INFO      c,
        CLMS_POLICY_INFO     m,
        CLMS_REPORT_CUSTOMER b,
        CLM_REPORT_ACCIDENT  d,
        CLM_WHOLE_CASE_BASE  e
        where
        c.report_date between
        str_to_date(#{reportDateBegin,jdbcType=VARCHAR},
        '%Y-%m-%d %H:%i:%s')
        and str_to_date(#{reportDateEnd,jdbcType=VARCHAR},
        '%Y-%m-%d %H:%i:%s')
        and m.department_code in(
        select
        dd.department_code from
        department_define dd where dd.department_code like
        concat('%', #{departmentCode,jdbcType=VARCHAR}, '%')
        )
        <if test="reporterName != null">
            and b.name = #{reporterName,jdbcType=VARCHAR}
        </if>
        and c.report_no = m.report_no
        and c.report_no = d.report_no
        and c.report_no = e.report_no
        and c.report_no = b.report_no
    </select>
    <select id="getHistoryCaseNew"
            resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO"
            parameterType="com.paic.ncbs.claim.model.vo.endcase.WholeCaseVO">
        WITH
        subtaskinfos AS (
        SELECT
        ci.report_no,
        ci.case_times,
        ci.TASK_DEFINITION_BPM_KEY,
        ci.status
        FROM clms_task_info ci
        WHERE ci.status != '1'
        AND ci.TASK_DEFINITION_BPM_KEY IN (
        'OC_COMMUNICATE', 'OC_MAJOR_INVESTIGATE', 'OC_CLAIM_SECOND_UNDERWRITING',
        'OC_ESTIMATE_CHANGE_REVIEW', 'OC_PREPAY_REVIEW', 'OC_WAIT_CUSTOMER_SUPPLEMENTS',
        'OC_INVESTIGATE_APPROVAL','OC_INVESTIGATE_REVIEW'
        )
        ),
        maintaskinfos AS (
        SELECT
        ci.report_no,
        ci.case_times,
        ci.TASK_DEFINITION_BPM_KEY
        FROM clms_task_info ci
        WHERE ci.status != '1'
        AND ci.TASK_DEFINITION_BPM_KEY IN (
        'OC_REPORT_TRACK', 'OC_CHECK_DUTY', 'OC_MANUAL_SETTLE', 'OC_SETTLE_REVIEW'
        )
        )
        SELECT DISTINCT
        crc.report_no AS reportNo,
        cri.report_date AS reportDate,
        cra.accident_date AS accidentDate,
        cwcb.whole_case_status AS caseStatus,
        cwcb.is_register AS isRegister,
        cwcb.case_times AS caseTimes,
        crc.client_type AS clientType,
        crc.client_type_name AS clientTypeName,
        crc.name AS insuredName,
        crc.client_cluster AS personnelAttribute,
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM subtaskinfos st
        WHERE st.report_no = cwcb.report_no
        AND st.case_times = cwcb.case_times
        ) THEN
        CASE
        WHEN EXISTS (
        SELECT 1
        FROM maintaskinfos mt
        JOIN clms_task_conflict ctc
        ON ctc.TASK_DEFINITION_BPM_KEY = mt.TASK_DEFINITION_BPM_KEY
        AND ctc.plan_operation = '1'
        AND ctc.constraint_type = '1'
        WHERE mt.report_no = cwcb.report_no
        AND mt.case_times = cwcb.case_times
        AND EXISTS (
        SELECT 1
        FROM subtaskinfos st
        WHERE st.report_no = cwcb.report_no
        AND st.case_times = cwcb.case_times
        AND st.TASK_DEFINITION_BPM_KEY = ctc.conflict_task_key
        and st.status in ('0','3')
        )
        ) THEN '10'
        ELSE '11'
        END
        ELSE '00'
        END AS subProcessFlag
        <if test="(policyNo != null and policyNo != '') or (caseNo != null and caseNo != '')">,
        cpi.CASE_NO caseNo
        </if>
        from CLM_REPORT_INFO cri,
        CLM_WHOLE_CASE_BASE cwcb,
        CLM_REPORT_ACCIDENT cra,
        CLMS_REPORT_CUSTOMER crc
        <if test="(policyNo != null and policyNo != '') or (caseNo != null and caseNo != '')">,
        CLMS_POLICY_INFO cpi
        </if>
        <if test="acceptanceNo != null and acceptanceNo != ''">,
            clms_report_info_ex crie
        </if>
        where 1=1
        <if test="acceptanceNo != null and acceptanceNo != ''">
            and crie.acceptance_no = #{acceptanceNo,jdbcType=VARCHAR}
        </if>
        <if test="certificateNo != null and certificateNo != '' ">
            and crc.certificate_no = #{certificateNo,jdbcType=VARCHAR}
        </if>
        <if test="certificateType != null and certificateType != '' ">
                and crc.certificate_type = #{certificateType,jdbcType=VARCHAR}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            and cri.report_no = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null and policyNo != '' ">
            and cpi.policy_no = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != '' ">
            and crc.name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="clientNo != null and clientNo != '' ">
                and crc.client_no = #{clientNo,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null and caseNo != ''">
            and cpi.case_no = #{caseNo,jdbcType=VARCHAR}
        </if>
        and crc.report_no=cri.report_no
        and crc.report_no=cwcb.report_no
        and crc.report_no=cra.report_no
        and cri.report_no = cra.report_no
        <if test="(policyNo != null and policyNo != '') or (caseNo != null and caseNo != '')">
            and cpi.report_no=cri.report_no
        </if>
        <if test="acceptanceNo != null and acceptanceNo != ''">
            and crie.report_no=cri.report_no
        </if>
        <if test="departmentCodes != null and departmentCodes != '' ">
            and cri.ACCEPT_DEPARTMENT_CODE  in
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getHistoryByPolicyNos" resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO" >
        select
        c.report_no reportNo,
        i.report_date reportDate,
        a.accident_date accidentDate,
        w.whole_case_status caseStatus,
        w.case_times caseTimes,
        c.client_type clientType,
        c.client_type_name
        clientTypeName,
        c.name insuredName
        from CLM_REPORT_INFO i,
        CLM_WHOLE_CASE_BASE w,
        CLM_REPORT_ACCIDENT a,
        CLMS_REPORT_CUSTOMER c,
        CLMS_POLICY_INFO p
        where
        (
        <foreach collection="customerInfo" separator="or" open="(" close=")" item="item">
             c.`NAME`  = #{item.name,jdbcType=VARCHAR} and
             c.`CERTIFICATE_NO`  = #{item.certificateNo,jdbcType=VARCHAR}
        </foreach>
        )
          and  p.policy_no = #{policyNo}
          and c.report_no=i.report_no
          and c.report_no=w.report_no
          and c.report_no=a.report_no
          and i.report_no = a.report_no
          and  c.report_no =p.report_no
    </select>
    <select id="getHistoryCaseNewByCopy" resultType="com.paic.ncbs.claim.model.dto.report.HistoryCaseDTO">
        select distinct
        crc.report_no reportNo,
        cri.report_date reportDate,
        cra.accident_date accidentDate,
        cwcb.whole_case_status caseStatus,
        cwcb.is_register isRegister,
        cwcb.case_times caseTimes,
        crc.client_type clientType,
        crc.client_type_name clientTypeName,
        crc.name insuredName,
        crc.client_cluster personnelAttribute
        <if test="(policyNo != null and policyNo != '') or (caseNo != null and caseNo != '')">,
            cpi.CASE_NO caseNo
        </if>
        from CLM_REPORT_INFO cri,
        CLM_WHOLE_CASE_BASE cwcb,
        CLM_REPORT_ACCIDENT cra,
        CLMS_REPORT_CUSTOMER crc
        <if test="(policyNo != null and policyNo != '') or (caseNo != null and caseNo != '')">,
            CLMS_POLICY_INFO cpi
        </if>
        where 1=1
        <if test="certificateNo != null and certificateNo != '' ">
            and crc.certificate_no = #{certificateNo,jdbcType=VARCHAR}
        </if>
        <if test="reportNo != null and reportNo != '' ">
            and cri.report_no = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null and policyNo != '' ">
            and cpi.policy_no = #{policyNo,jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != '' ">
            and crc.name = #{name,jdbcType=VARCHAR}
        </if>
        <if test="caseNo != null and caseNo != ''">
            and cpi.case_no = #{caseNo,jdbcType=VARCHAR}
        </if>
        and crc.report_no=cri.report_no
        and crc.report_no=cwcb.report_no
        and crc.report_no=cra.report_no
        and cri.report_no = cra.report_no
        <if test="(policyNo != null and policyNo != '') or (caseNo != null and caseNo != '')">
            and cpi.report_no=cri.report_no
        </if>
    </select>

    <insert id="insertList" parameterType="java.util.List">
        insert into CLM_REPORT_INFO (ID_CLM_REPORT_INFO,
        CREATED_BY,
        CREATED_DATE,
        UPDATED_BY, UPDATED_DATE, REPORT_NO,
        REPORT_MODE, REPORT_DATE, DRIVER_NAME,
        IS_CARGO_LOSS, IS_CAR_LOSS,
        IS_INJURED,
        REPORT_ON_PORT, IS_THIRD_AGENT_REPORT, IS_AGENT_CASE,
        REPORTER_NAME, REPORTER_CALL_NO, REPORTER_REGISTER_TEL,
        REPORT_REGISTER_UM, ACCEPT_DEPARTMENT_CODE,
        REPORT_OPTION,
        CANCEL_REPORT_DATE, REMARK,
        MIGRATE_FROM, IS_WAIT_CALL,
        FLAG_SELF_SERVICE, REPAIRE_FACTORY_ID, REPAIR_FACTORY_NAME,
        BJ_APP_CASE_NUMBER, REPORTED_LOSS_AMOUNT, IS_ACCEPT_DIRECT_PAY,
        IS_COMMUNITY_SURVEY,DRIVE_SEX,DRIVE_CARD_ID,REPORT_TYPE,ARCHIVE_DATE)
        values
        <foreach collection="list" separator="," index="index" item="item">
            (#{item.idClmReportInfo,jdbcType=VARCHAR}, 
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdDate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedDate,jdbcType=TIMESTAMP},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.reportMode,jdbcType=VARCHAR}, 
            #{item.reportDate,jdbcType=TIMESTAMP},
            #{item.driverName,jdbcType=VARCHAR},
            #{item.isCargoLoss,jdbcType=VARCHAR},
            #{item.isCarLoss,jdbcType=VARCHAR}, 
            #{item.isInjured,jdbcType=VARCHAR},
            #{item.reportOnPort,jdbcType=VARCHAR},
            #{item.isThirdAgentReport,jdbcType=VARCHAR},
            #{item.isAgentCase,jdbcType=VARCHAR},
            #{item.reporterName,jdbcType=VARCHAR},
            #{item.reporterCallNo,jdbcType=VARCHAR},
            #{item.reporterRegisterTel,jdbcType=VARCHAR},
            #{item.reportRegisterUm,jdbcType=VARCHAR},
            #{item.acceptDepartmentCode,jdbcType=VARCHAR},
            #{item.reportOption,jdbcType=VARCHAR},
            #{item.cancelReportDate,jdbcType=TIMESTAMP}, 
            #{item.remark,jdbcType=VARCHAR},
            #{item.migrateFrom,jdbcType=VARCHAR}, 
            #{item.isWaitCall,jdbcType=VARCHAR},
            #{item.flagSelfService,jdbcType=VARCHAR},
            #{item.repaireFactoryId,jdbcType=VARCHAR},
            #{item.repairFactoryName,jdbcType=VARCHAR},
            #{item.bjAppCaseNumber,jdbcType=VARCHAR},
            #{item.reportedLossAmount,jdbcType=DECIMAL},
            #{item.isAcceptDirectPay,jdbcType=VARCHAR},
            #{item.isCommunitySurvey,jdbcType=VARCHAR},
            #{item.driveSex,jdbcType=VARCHAR},
            #{item.driveCardId,jdbcType=VARCHAR},
            #{item.reportType,jdbcType=VARCHAR},now())
        </foreach>
    </insert>
    <select id="getProductNames" resultType="java.lang.String" parameterType="java.lang.String">
        select  distinct  PRODUCT_NAME  from clms_policy_info cpi where REPORT_NO =#{reportNo}
    </select>
    <select id="getCusPolicyHistoryClaimInfo"
            resultType="com.paic.ncbs.claim.model.vo.openapi.ClaimHistoryReportInfoVO"
            parameterType="com.paic.ncbs.claim.model.vo.openapi.QueryClaimReportRequestVo">
        select distinct cpcc.REPORT_NO
        from clms_policy_claim_case cpcc
        <if test="certificateNo != null and certificateNo !='' ">
            , CLMS_REPORT_CUSTOMER crc
        </if>
        <where>
            <if test="certificateNo != null and certificateNo != '' ">
                and cpcc.REPORT_NO=crc.REPORT_NO
                and crc.CERTIFICATE_TYPE = #{certificateType}
                and crc.certificate_no = #{certificateNo}
            </if>
            <if test="reportNo != null and reportNo != ''">
                and cpcc.report_no=#{reportNo}
            </if>
            <if test="policyNo != null and policyNo != ''">
                and cpcc.policy_no=#{policyNo}
            </if>
            <if test="caseNo != null and caseNo!= '' ">
                and cpcc.CASE_NO=#{caseNo}
            </if>
        </where>

    </select>
    <select id="getPolicyHistoryClaimInfo"
            resultType="com.paic.ncbs.claim.model.vo.openapi.ClaimHistoryReportInfoVO"
            parameterType="com.paic.ncbs.claim.model.vo.openapi.QueryClaimReportRequestVo">
        select distinct cpcc.REPORT_NO,
        cpi.REPORTER_NAME,
        cpi.REPORT_DATE,
        cwcb.WHOLE_CASE_STATUS caseStatus,
        cwcb.END_CASE_DATE
        from clms_policy_claim_case cpcc ,CLM_REPORT_INFO cpi,CLM_WHOLE_CASE_BASE cwcb
        <if test="certificateNo != null and certificateNo !='' ">
            , CLMS_REPORT_CUSTOMER crc
        </if>
        where cpcc.REPORT_NO =cpi.REPORT_NO
        and   cpcc.REPORT_NO =cwcb.REPORT_NO
        <if test="certificateNo != null and certificateNo != '' ">
            and   cpcc.REPORT_NO=crc.REPORT_NO
            and crc.CERTIFICATE_TYPE = #{certificateType}
            and crc.certificate_no = #{certificateNo}
        </if>
        <if test="reportNo != null and reportNo != ''">
            and cpcc.report_no=#{reportNo}
        </if>
        <if test="policyNo != null and policyNo != ''">
            and cpcc.policy_no=#{policyNo}
        </if>
        <if test="caseNo != null and caseNo!= '' ">
            and cpcc.CASE_NO=#{caseNo}
        </if>
    </select>
    <!-- 根据报案号赔付次数查询赔付总金额 -->
    <select id="getSumPayFee" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(pp.POLICY_SUM_PAY) , 0) amt ,pp.CASE_TIMES
        FROM CLM_POLICY_PAY pp
        WHERE pp.REPORT_NO=#{reportNo}
        GROUP BY pp.CASE_TIMES
        ORDER  BY pp.CASE_TIMES  DESC LIMIT 1
    </select>

    <select id="queryReportInfoUnsettled" resultMap="reportQueryInfo"
            parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO">
        select
        distinct a.REPORT_NO,
        a.POLICY_NO,
        '1' as caseStatus
        from clm_case_base a
        inner join CLMS_REPORT_CUSTOMER b on b.REPORT_NO = a.REPORT_NO
        inner join CLM_WHOLE_CASE_BASE e on e.REPORT_NO = a.REPORT_NO and e.WHOLE_CASE_STATUS &lt;&gt; '0'
        inner join CLMS_POLICY_INFO pi on pi.REPORT_NO = a.REPORT_NO
        inner join CLMS_POLICY_PLAN pp on pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
        inner join CLMS_POLICY_DUTY pd on pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN
        where 1=1
        <if test="certificateNos != null and certificateNos.size() != 0 ">
            and b.CERTIFICATE_NO in
            <foreach collection="certificateNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="policyNo != null ">
            and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and pp.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and pd.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryReportInfoSettled" resultMap="reportQueryInfo"
            parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO">
        select
        distinct a.REPORT_NO,
        a.POLICY_NO,
        '2' as caseStatus
        from clm_case_base a
        inner join CLMS_REPORT_CUSTOMER b on b.REPORT_NO = a.REPORT_NO
        inner join clm_plan_duty_pay d on d.CASE_NO = a.CASE_NO and d.DUTY_PAY_AMOUNT is not null and d.DUTY_PAY_AMOUNT!=0
        inner join CLM_WHOLE_CASE_BASE e on e.REPORT_NO = a.REPORT_NO and e.WHOLE_CASE_STATUS = '0' and e.INDEMNITY_CONCLUSION = '1'
        where 1=1
        <if test="certificateNos != null and certificateNos.size() != 0 ">
            and b.CERTIFICATE_NO in
            <foreach collection="certificateNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="policyNo != null ">
            and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and d.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and d.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryReportInfoZeroCannel" resultMap="reportQueryInfo"
            parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO">
        SELECT DISTINCT
        a.REPORT_NO,
        a.POLICY_NO,
        '3' AS caseStatus
        FROM clm_case_base a
        INNER JOIN CLMS_REPORT_CUSTOMER b ON b.REPORT_NO = a.REPORT_NO
        INNER JOIN CLM_WHOLE_CASE_BASE e ON e.REPORT_NO = a.REPORT_NO AND e.WHOLE_CASE_STATUS = '0'
        INNER JOIN CLMS_POLICY_INFO pi ON pi.REPORT_NO = a.REPORT_NO
        INNER JOIN CLMS_POLICY_PLAN pp ON pp.ID_AHCS_POLICY_INFO = pi.ID_AHCS_POLICY_INFO
        INNER JOIN CLMS_POLICY_DUTY pd ON pd.ID_AHCS_POLICY_PLAN = pp.ID_AHCS_POLICY_PLAN
        WHERE e.INDEMNITY_CONCLUSION IN ( '2', '5', '4' )
        <if test="certificateNos != null and certificateNos.size() != 0 ">
            and b.CERTIFICATE_NO in
            <foreach collection="certificateNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="policyNo != null ">
            and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and pp.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and pd.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        UNION ALL
        SELECT DISTINCT
        a.REPORT_NO,
        a.POLICY_NO,
        '4' AS caseStatus
        FROM
        clm_case_base a
        INNER JOIN CLMS_REPORT_CUSTOMER b ON b.REPORT_NO = a.REPORT_NO
        INNER JOIN clm_policy_pay p ON p.CASE_NO = a.CASE_NO
        INNER JOIN clm_plan_duty_pay d ON d.CASE_NO = a.CASE_NO
        INNER JOIN CLM_WHOLE_CASE_BASE e ON e.REPORT_NO = a.REPORT_NO
        AND e.WHOLE_CASE_STATUS = '0'
        WHERE e.INDEMNITY_CONCLUSION = '1'
        AND ifnull(p.POLICY_PAY,0) = 0
        <if test="certificateNos != null and certificateNos.size() != 0 ">
            and b.CERTIFICATE_NO in
            <foreach collection="certificateNos" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="policyNo != null ">
            and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and d.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and d.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listReportQueryInfoUnsettled" resultMap="reportQueryInfo" parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO">
        select
        distinct a.REPORT_NO,
        a.POLICY_NO,
        '1' as caseStatus
        from clm_case_base a
        inner join CLMS_REPORT_CUSTOMER b on b.REPORT_NO = a.REPORT_NO
        inner join CLM_WHOLE_CASE_BASE e on e.REPORT_NO = a.REPORT_NO and e.WHOLE_CASE_STATUS &lt;&gt; '0'
        where 1=1
        <if test="clientNo != null ">
            and b.CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateNo != null ">
            and b.CERTIFICATE_NO = #{certificateNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateType != null ">
            and b.CERTIFICATE_TYPE = #{certificateType, jdbcType=VARCHAR}
        </if>
        <if test="name != null ">
            and b.NAME = #{name, jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null ">
            and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        </if>
    </select>

    <select id="listReportQueryInfoSettled" resultMap="reportQueryInfo" parameterType="com.paic.ncbs.claim.model.dto.openapi.ReportQueryReqDTO">
        select
        distinct a.REPORT_NO,
        a.POLICY_NO,
        '2' as caseStatus
        from clm_case_base a
        inner join CLMS_REPORT_CUSTOMER b on b.REPORT_NO = a.REPORT_NO
        inner join clm_plan_duty_pay d on d.CASE_NO = a.CASE_NO and d.DUTY_PAY_AMOUNT is not null and d.DUTY_PAY_AMOUNT!=0
        inner join CLM_WHOLE_CASE_BASE e on e.REPORT_NO = a.REPORT_NO and e.WHOLE_CASE_STATUS = '0' and e.INDEMNITY_CONCLUSION = '1'
        where 1=1
        <if test="clientNo != null ">
            and b.CLIENT_NO = #{clientNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateNo != null ">
            and b.CERTIFICATE_NO = #{certificateNo, jdbcType=VARCHAR}
        </if>
        <if test="certificateType != null ">
            and b.CERTIFICATE_TYPE = #{certificateType, jdbcType=VARCHAR}
        </if>
        <if test="name != null ">
            and b.NAME = #{name, jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null ">
            and a.POLICY_NO = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="planCodes != null ">
            and d.PLAN_CODE in
            <foreach collection="planCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="dutyCodes != null ">
            and d.DUTY_CODE in
            <foreach collection="dutyCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getProcessStatus" parameterType="java.lang.String" resultType="java.lang.String" >
        select PROCESS_STATUS from 	CLMS_case_process
        where REPORT_NO=#{reportNo}
        ORDER BY CASE_TIMES desc limit 1
    </select>

    <update id="updateReportRiskFlag" parameterType="com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity">
        update clm_report_info
        set  RISK_FLAG=#{riskFlag} ,RISK_REMARK=#{riskRemark}
        where REPORT_NO=#{reportNo}
    </update>

    <select id="getReportNoList" resultType="java.lang.String" parameterType="com.paic.ncbs.claim.model.vo.report.ReportQueryVO">
        select
        	distinct
        	c.report_no
        from clm_report_accident a, clms_report_customer c, clms_policy_info p
        where c.report_no = a.report_no and c.report_no = p.report_no
        <if test="certificateType != null ">
            and c.certificate_type = #{certificateType, jdbcType=VARCHAR}
        </if>
        <if test="certificateNo != null ">
            and c.certificate_no = #{certificateNo, jdbcType=VARCHAR}
        </if>
        <if test="policyNo != null ">
            and p.policy_no = #{policyNo, jdbcType=VARCHAR}
        </if>
        <if test="accidentDate != null ">
            and date(a.accident_date) = date(#{accidentDate,jdbcType=DATE})
        </if>
    </select>
    <select id="getReportInfoCount" parameterType="com.paic.ncbs.claim.model.dto.report.QueryAccidentVo" resultType="java.lang.String">
        select distinct ri.REPORT_NO from clm_report_info ri join clm_case_base cb on ri.REPORT_NO = cb.REPORT_NO
        join clm_policy_pay pp on cb.REPORT_NO = pp.REPORT_NO
        where cb.policy_no = #{policyNo}
        and ri.REPORT_DATE <![CDATA[<=]]> #{endDate}
        and ri.REPORT_DATE <![CDATA[>=]]> #{startDate}
        and cb.CASE_STATUS = '0'
        and cb.INDEMNITY_CONCLUSION = '1'
        and pp.POLICY_SUM_PAY > 0;
    </select>
    <select id="getIdentifyCaseCount" resultType="java.lang.Integer">
        select count(*)
        from clms_report_info_ex crie
        join clm_whole_case_base cwcb on crie.report_no = cwcb.report_no
        join clms_policy_info cpi on cwcb.report_no = cpi.report_no
        where 1=1
        and crie.COMPANY_ID = 'AiModel'
        and cwcb.CREATED_DATE between CURDATE() and DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and cwcb.WHOLE_CASE_STATUS != '1'
        and cpi.PRODUCT_PACKAGE_TYPE in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getIdentifyCaseCountYesterday" resultType="java.lang.Integer">
        select count(*)
        from clms_report_info_ex crie
        join clm_whole_case_base cwcb on crie.report_no = cwcb.report_no
        join clms_policy_info cpi on cwcb.report_no = cpi.report_no
        where 1=1
        and crie.COMPANY_ID = 'AiModel'
        and cwcb.CREATED_DATE between DATE_SUB(CURDATE(), INTERVAL 1 DAY) and CURDATE()
        and cwcb.WHOLE_CASE_STATUS != '1'
        and cpi.PRODUCT_PACKAGE_TYPE in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getInRecognitionCount" resultType="java.lang.Integer">
        select count(*)
        from clms_report_info_ex crie
        join clms_task_info cti on crie.report_no = cti.report_no
        join clms_policy_info cpi on crie.report_no = cpi.report_no
        where 1=1
        and crie.COMPANY_ID = 'AiModel'
        and crie.CREATED_DATE between CURDATE() and DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and cti.TASK_DEFINITION_BPM_KEY = 'OC_REPORT_TRACK'
        and cti.STATUS = '0'
        and cpi.PRODUCT_PACKAGE_TYPE in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getAllCaseCount" resultType="java.lang.Integer">
        select count(*)
        from clm_whole_case_base cwcb
        join clms_policy_info cpi on cwcb.report_no = cpi.report_no
        where 1=1
        and cwcb.CREATED_DATE between DATE_SUB(CURDATE(), INTERVAL 1 DAY) and CURDATE()
        and cwcb.WHOLE_CASE_STATUS = '0'
        and cpi.PRODUCT_PACKAGE_TYPE in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getCaseInputDuration" resultType="java.math.BigDecimal">
        select
        IFNULL(sum(ROUND(TIMESTAMPDIFF(SECOND, crie.created_date, cor.sys_ctime) / 3600.0, 2)),0) caseInputDuration
        from clms_report_info_ex crie
        inner join clm_operation_record cor on crie.report_no = cor.report_no
        where cor.operation_node = 'OC_CHECK_DUTY'
        and cor.description = '提交'
        and crie.report_no = #{reportNo,jdbcType=VARCHAR}
    </select>
    <select id="isAIModel" resultType="java.lang.String">
        select
        (case when crie.COMPANY_ID = 'AiModel' then '是' else '否' end) as isAIModel
        from clms_report_info_ex crie
        where crie.report_no = #{reportNo,jdbcType=VARCHAR}
    </select>
    <select id="getReportCaseCount" resultType="java.lang.Integer">
        select count(*)
        from clms_policy_info cpi
        where 1=1
        and cpi.CREATED_DATE between CURDATE() and DATE_ADD(CURDATE(), INTERVAL 1 DAY)
        and cpi.PRODUCT_PACKAGE_TYPE in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getGlobalReportDto" resultType="com.paic.ncbs.claim.model.dto.copypolicy.GlobalReportDto">
        select
                DATE_FORMAT(cra.ACCIDENT_DATE,'%Y%m%d') accdDate,
                DATE_FORMAT(cra.ACCIDENT_DATE,'%H%i%s') accdTime,
                cra.accident_county_code lossLocAreaCode,
                cra.ACCIDENT_place lossLocDetl,
                cra.ACCIDENT_DETAIL  claimDescription,
                crie.LINK_MAN_RELATION ntfCls,
                cri.REPORTER_NAME ntfName,
                crie.RELATION_WITH_REPORTER ntfIdType,
                MID(cri.REPORTER_REGISTER_TEL,1,3) contactNo21,
                MID(cri.REPORTER_REGISTER_TEL,4,4) contactNo22,
                MID(cri.REPORTER_REGISTER_TEL,8,4) contactNo23,
                DATE_FORMAT(cra.ACCIDENT_DATE,'%Y%m%d') referAgrmDate,
                cpi.policy_no agrmNo,
                crc.CLIENT_NO  pibojaId,
                crc.NAME pibojaName,
                crc.CERTIFICATE_NO pibojaIdentifyNo,
                cph.CLIENT_NO contractorCd,
                cph.NAME contractorName
                from clm_report_accident cra,
                clms_report_info_ex crie,
                clm_report_info cri,
                clms_policy_info cpi,
                clms_report_customer crc,
                clms_policy_holder cph
                where cra.report_no = crie.report_no
                and cri.report_no = cra.report_no
                and cpi.REPORT_NO = cra.REPORT_NO
                and crc.REPORT_NO = cra.REPORT_NO
                and cpi.ID_AHCS_POLICY_INFO  = cph.ID_AHCS_POLICY_info
                and cra.REPORT_NO = #{reportNo};
    </select>
    <select id="getCopyReqVo" resultType="com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO">
        select cpi.POLICY_NO policyNo,cip.NAME name,cip.CERTIFICATE_NO certificateNo from clms_policy_info cpi,clms_insured_person cip  where cpi.ID_AHCS_POLICY_INFO = cip.ID_AHCS_POLICY_INFO and cpi.POLICY_NO =#{policyNo} and REPORT_NO =#{reportNo};
    </select>
</mapper>